buildscript {
    dependencies {
        classpath 'com.google.gms:google-services:4.4.2'
    }
}
plugins {
    id 'com.android.application' version '8.2.1' apply false
    id 'com.google.gms.google-services'
}
allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

rootProject.buildDir = "../build"
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
}
subprojects {
    project.evaluationDependsOn(":app")
}

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}
