org.gradle.jvmargs=-Xmx4G -XX:MaxMetaspaceSize=2G -XX:+HeapDumpOnOutOfMemoryError
android.useAndroidX=true
android.enableJetifier=false
org.gradle.java.home=C:/Program Files/Java/jdk-21

# Configurações de performance do Gradle
org.gradle.daemon=true
org.gradle.parallel=true
org.gradle.configureondemand=true
org.gradle.caching=true

# Suporte para páginas de memória de 16KB (Android 15+)
android.experimental.enableArtProfiles=true
android.experimental.r8.dex-startup-optimization=true
android.experimental.enableComposeCompilerMetrics=true

# Configurações para resolver problemas de compilação
android.suppressUnsupportedCompileSdk=34
android.nonTransitiveRClass=false

# Resolver FileSystemAlreadyExistsException
android.enableResourceOptimizations=false