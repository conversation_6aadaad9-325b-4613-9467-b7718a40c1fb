import 'package:flutter/material.dart';

class SearchHeaderDelegate extends SliverPersistentHeaderDelegate {
  final Widget child;
  final double minHeigth;

  SearchHeaderDelegate({required this.child, required this.minHeigth});

  @override
  Widget build(BuildContext context, double shrinkOffset, bool overlapsContent) {
    return child;
  }

  @override
  double get maxExtent => minHeigth;

  @override
  double get minExtent => minHeigth;

  @override
  bool shouldRebuild(covariant SearchHeaderDelegate oldDelegate) {
    return child != oldDelegate.child || minHeigth != oldDelegate.minHeigth;
  }
}
