import 'package:flutter/widgets.dart';
import 'package:flutter_svg/svg.dart';

import '../../../../../../theme/color_outlet.dart';
import '../../../../../../theme/svg_icons.dart';
import '../../../../../components/text_pattern.dart';

class BenefitCard extends StatelessWidget {
  final String? cupom;
  final bool? frete;
  final bool? menorPreco;
  final String plataforma;
  final bool ofertaIndisponivel;

  const BenefitCard({
    super.key,
    this.cupom,
    this.frete,
    this.menorPreco,
    required this.plataforma,
    required this.ofertaIndisponivel,
  });

  @override
  Widget build(BuildContext context) {
    if (ofertaIndisponivel == true) {
      return _buildOfertaIndisponivel();
    } else if (cupom != null && cupom!.trim().isNotEmpty && cupom != 'Cupom não disponível' && cupom != 'Sem cupom') {
      return _buildCupomCard();
    } else if (frete ?? false) {
      return _buildFreteCard(plataforma);
    } else if (menorPreco ?? false) {
      return _buildMenorPrecoCard();
    } else {
      return const SizedBox.shrink(); // No benefits to display
    }
  }

  Widget _buildCupomCard() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: ColorOutlet.contentSecondary.withAlpha(10),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          SvgPicture.asset(
            SvgIcons.financeCoupon,
            width: 16,
            height: 16,
            colorFilter: ColorFilter.mode(ColorOutlet.contentSecondary, BlendMode.srcIn),
          ),
          const SizedBox(width: 4),
          TextPattern.customText(
            text: 'TEM CUPOM',
            color: ColorOutlet.contentSecondary,
            fontSize: 11,
            fontWeightOption: FontWeightOption.semiBold,
          ),
        ],
      ),
    );
  }

  Widget _buildFreteCard(String plataforma) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: ColorOutlet.contentSecondary.withAlpha(10),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          SvgPicture.asset(
            plataforma == 'Amazon' ? SvgIcons.iconeAmazon : SvgIcons.markerShipping,
            width: 16,
            height: 16,
            colorFilter: ColorFilter.mode(ColorOutlet.contentSecondary, BlendMode.srcIn),
          ),
          const SizedBox(width: 4),
          TextPattern.customText(
            text: plataforma == 'Amazon' ? 'FRETE GRÁTIS' : 'FRETE GRÁTIS',
            color: ColorOutlet.contentSecondary,
            fontSize: 11,
            fontWeightOption: FontWeightOption.semiBold,
          ),
        ],
      ),
    );
  }

  Widget _buildMenorPrecoCard() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: ColorOutlet.contentSecondary.withAlpha(10),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          SvgPicture.asset(
            SvgIcons.financeDiscount,
            width: 16,
            height: 16,
            colorFilter: ColorFilter.mode(ColorOutlet.contentSecondary, BlendMode.srcIn),
          ),
          const SizedBox(width: 4),
          TextPattern.customText(
            text: 'MELHOR OFERTA',
            color: ColorOutlet.contentSecondary,
            fontSize: 11,
            fontWeightOption: FontWeightOption.semiBold,
          ),
        ],
      ),
    );
  }
}

Widget _buildOfertaIndisponivel() {
  return Container(
    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
    decoration: BoxDecoration(
      color: ColorOutlet.contentSecondary.withAlpha(10),
      borderRadius: BorderRadius.circular(8),
    ),
    child: Row(
      children: [
        SvgPicture.asset(
          SvgIcons.financeDiscountBroken,
          width: 16,
          height: 16,
          colorFilter: ColorFilter.mode(ColorOutlet.contentSecondary, BlendMode.srcIn),
        ),
        const SizedBox(width: 4),
        TextPattern.customText(
          text: 'OFERTA INDISPONÍVEL',
          color: ColorOutlet.contentSecondary,
          fontSize: 11,
          fontWeightOption: FontWeightOption.semiBold,
        ),
      ],
    ),
  );
}
