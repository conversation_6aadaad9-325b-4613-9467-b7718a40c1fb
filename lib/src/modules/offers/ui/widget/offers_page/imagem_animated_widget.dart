import 'package:flutter/material.dart';
import '../../../../../models/categorias_menu.dart';

class ImagemAnimatedWidget extends StatefulWidget {
  final CategoriaMenu categoria;

  const ImagemAnimatedWidget({super.key, required this.categoria});

  @override
  State<ImagemAnimatedWidget> createState() => _ImagemAnimatedWidgetState();
}

class _ImagemAnimatedWidgetState extends State<ImagemAnimatedWidget> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _verticalAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();

    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 15),
    )..repeat(reverse: true);

    _verticalAnimation = Tween<double>(begin: 0.0, end: -50.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _scaleAnimation = Tween<double>(begin: 1.2, end: 1.4).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // Imagem com animação
        AnimatedBuilder(
          animation: _animationController,
          builder: (context, child) {
            return Transform.translate(
              offset: Offset(0, _verticalAnimation.value),
              child: Transform.scale(
                scale: _scaleAnimation.value,
                child: Image.asset(
                  widget.categoria.fotoGrande!,
                  fit: BoxFit.cover,
                  width: double.infinity,
                  height: double.infinity,
                ),
              ),
            );
          },
        ),
        // Gradiente na parte inferior
        Positioned.fill(
          child: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Colors.transparent,
                  Colors.black.withValues(alpha: 0.9),
                ],
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
              ),
            ),
          ),
        ),
      ],
    );
  }
}
