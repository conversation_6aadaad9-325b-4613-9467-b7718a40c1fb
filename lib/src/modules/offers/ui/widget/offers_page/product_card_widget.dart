import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../../../../../../../theme/color_outlet.dart';
import '../../../../../../theme/svg_icons.dart';
import '../../../../../components/black_and_white_filter.dart';
import '../../../../../components/flag_disabled.dart';
import '../../../../../components/text_pattern.dart';
import '../../../../../helpers/plataform_icons.dart';
import '../../../../../models/product.dart';
import '../../../../../services/navigation/scroll_services.dart';
import '../../../controllers/offers_controller.dart';
import '../../../offers_module.dart';
import '../product_details/product_image_box.dart';
import '../product_details/styled_logo_container.dart';
import 'benifit_card.dart';
import 'like_button.dart';

class ProductCardWidget extends StatefulWidget {
  final Product product;
  final double? height;
  final double? width;

  const ProductCardWidget({
    this.width,
    this.height,
    super.key,
    required this.product,
  });

  static SliverChildBuilderDelegate buildSliverChildBuilderDelegate(
    List<Product> products,
  ) {
    return SliverChildBuilderDelegate((context, index) {
      return Padding(
        padding: const EdgeInsets.symmetric(
          horizontal: 16.0,
          vertical: 8.0,
        ),
        child: ProductCardWidget(product: products[index]),
      );
    }, childCount: products.length);
  }

  @override
  State<ProductCardWidget> createState() => _ProductCardWidgetState();
}

class _ProductCardWidgetState extends State<ProductCardWidget>
    with SingleTickerProviderStateMixin {
  final OffersController controller = Modular.get<OffersController>();
  final scrollService = Modular.get<ScrollService>();
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  bool showHeart = false;
  late String _timeAgo;

  @override
  void initState() {
    super.initState();
    _timeAgo = controller.getTimeToNow(widget.product.criadoEm);
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 200),
    );

    _scaleAnimation = Tween<double>(begin: 0.0, end: 1.0)
        .chain(CurveTween(curve: Curves.easeOut))
        .animate(_animationController);

    _initLikeState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        precacheImage(
          NetworkImage(widget.product.urlImagem),
          context,
        );
      }
    });
  }

  @override
  void didUpdateWidget(covariant ProductCardWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.product.criadoEm != widget.product.criadoEm) {
      _timeAgo = controller.getTimeToNow(widget.product.criadoEm);
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _initLikeState() async {
    final userEmail =
        Supabase.instance.client.auth.currentUser?.email ?? '';
    await controller.fetchLikesCount(widget.product.id);
    await controller.fetchLikeStatus(widget.product.id, userEmail);
  }

  void _toggleLike() {
    final userEmail =
        Supabase.instance.client.auth.currentUser?.email ?? '';
    controller.toggleLike(widget.product.id, userEmail);
  }

  void _handleDoubleTap() {
    if (mounted) {
      setState(() {
        showHeart = true;
      });
    }

    if (!controller.isProductLiked(widget.product.id)) {
      _toggleLike();
    }

    _animationController.forward(from: 0.0).then((_) {
      Future.delayed(const Duration(milliseconds: 300), () {
        if (mounted) {
          setState(() {
            showHeart = false;
          });
        }
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return InkWell(
      splashColor: ColorOutlet.systemBorderDisabled.withValues(
        alpha: 0.3,
      ),
      highlightColor: ColorOutlet.systemBorderDisabled.withValues(
        alpha: 0.3,
      ),
      borderRadius: BorderRadius.circular(24),
      onTap: () {
        scrollService.saveScrollPosition('offersPage');
        Modular.to.pushNamed(
          '/offers${OffersModule.productDetails}',
          arguments: widget.product,
        );
      },
      onDoubleTap: _handleDoubleTap,
      child: AnimatedBuilder(
        animation: controller,
        builder: (context, child) {
          return Stack(
            alignment: Alignment.center,
            children: [
              Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(24),
                  color: ColorOutlet.paper,
                ),
                padding: const EdgeInsets.only(
                  left: 16,
                  right: 16,
                  top: 16,
                  bottom: 8,
                ),
                child: Column(
                  children: [
                    Row(
                      children: [
                        Stack(
                          children: [
                            widget.product.invalidProduct
                                ? Stack(
                                  children: [
                                    BlackAndWhiteFilter(
                                      child: ProductImageBox(
                                        product: widget.product,
                                        height: 120,
                                        width: 120,
                                      ),
                                    ),
                                    Padding(
                                      padding: const EdgeInsets.only(
                                        top: 44,
                                      ),
                                      child: FlagDisabled(
                                        height: 32,
                                        text: 'Indisponível',
                                        width: 120,
                                        fontWeightOption:
                                            FontWeightOption.medium,
                                      ),
                                    ),
                                  ],
                                )
                                : ProductImageBox(
                                  product: widget.product,
                                  height: 120,
                                  width: 120,
                                ),
                            Positioned(
                              top: 8,
                              left: 8,
                              child: StyledLogoContainer(
                                padding: 4,
                                borderRadius: 8,
                                logo: PlatformIcons.fromName(
                                  widget.product.plataforma,
                                ),
                                plataformName:
                                    widget.product.plataforma,
                                height: 24,
                                width: 24,
                              ),
                            ),
                          ],
                        ),
                        SizedBox(width: 16),
                        Expanded(
                          flex: 2,
                          child: Column(
                            crossAxisAlignment:
                                CrossAxisAlignment.start,
                            children: [
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  BenefitCard(
                                    ofertaIndisponivel:
                                        widget.product.invalidProduct,
                                    cupom: widget.product.cupom,
                                    frete: widget.product.frete,
                                    menorPreco:
                                        widget.product.menorPreco,
                                    plataforma:
                                        widget.product.plataforma,
                                  ),
                                  TextPattern.customText(
                                    text: _timeAgo,
                                    color: Colors.grey,
                                    fontSize: 12,
                                    fontWeightOption:
                                        FontWeightOption.regular,
                                  ),
                                ],
                              ),
                              SizedBox(height: 8),
                              TextPattern.customText(
                                text: widget.product.titulo,
                                color: ColorOutlet.contentSecondary,
                                fontSize: 14,
                                fontWeightOption:
                                    FontWeightOption.medium,
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                              SizedBox(height: 8),
                              Visibility(
                                visible:
                                    widget.product.precoAntigo != 0 ||
                                    widget.product.precoAntigo >
                                        widget.product.precoAtual,
                                replacement: SizedBox(height: 20),
                                child: TextPattern.customText(
                                  text: controller
                                      .convertDoubleToString(
                                        widget.product.precoAntigo,
                                      ),
                                  fontSize: 14,
                                  fontWeightOption:
                                      FontWeightOption.regular,
                                  color: ColorOutlet.contentGhost,
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                  decoration:
                                      TextDecoration.lineThrough,
                                  decorationColor:
                                      ColorOutlet.contentGhost,
                                ),
                              ),
                              TextPattern.customText(
                                text: controller
                                    .convertDoubleToString(
                                      widget.product.precoAtual,
                                    ),
                                color: ColorOutlet.contentPrimary,
                                fontSize: 20,
                                fontWeightOption:
                                    FontWeightOption.bold,
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    Padding(
                      padding: EdgeInsets.only(top: 8),
                      child: Row(
                        mainAxisAlignment:
                            MainAxisAlignment.spaceBetween,
                        children: [
                          Transform.translate(
                            offset: Offset(-4, 0),
                            child: Row(
                              children: [
                                LikeButton(
                                  isLiked: controller.isProductLiked(
                                    widget.product.id,
                                  ),
                                  onTap: _toggleLike,
                                ),

                                TextPattern.customText(
                                  text:
                                      controller
                                          .getLikesCount(
                                            widget.product.id,
                                          )
                                          .toString(),
                                  color: ColorOutlet.contentSecondary,
                                  fontSize: 14,
                                  fontWeightOption:
                                      FontWeightOption.regular,
                                ),
                              ],
                            ),
                          ),
                          Spacer(),
                          TextPattern.customText(
                            text: 'Conferir oferta',
                            color: ColorOutlet.contentPrimary,
                            fontSize: 14,
                            fontWeightOption:
                                FontWeightOption.regular,
                          ),
                          const SizedBox(width: 4),
                          SvgPicture.asset(
                            SvgIcons.arrowRightTip,
                            width: 20,
                            height: 20,
                            colorFilter: ColorFilter.mode(
                              ColorOutlet.contentPrimary,
                              BlendMode.srcIn,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              if (showHeart)
                ScaleTransition(
                  scale: _scaleAnimation,
                  child: ShaderMask(
                    shaderCallback: (Rect bounds) {
                      return const RadialGradient(
                        center: Alignment.topLeft,
                        radius: 1.0,
                        colors: [
                          Color(0xFFFF8095),
                          Color(0xFFFF4060),
                          Color(0xFF802030),
                        ],
                        stops: [0.0, 0.5, 1.0],
                      ).createShader(bounds);
                    },
                    blendMode: BlendMode.srcIn,
                    child: Icon(
                      Icons.favorite,
                      color: Colors.white,
                      size: 100,
                    ),
                  ),
                ),
            ],
          );
        },
      ),
    );
  }
}
