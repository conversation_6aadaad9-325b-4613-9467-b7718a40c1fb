import 'package:flutter/material.dart';
import '../../../../../../theme/color_outlet.dart';
import '../../../../../components/text_pattern.dart';
import '../../../../../models/categorias_menu.dart';
import '../../../../../models/story_model.dart';
import 'double_border_icon.dart';
import 'story_carousel.dart';

class StoryCategoryAvatar extends StatelessWidget {
  const StoryCategoryAvatar({
    super.key,
    required this.loadingIndex,
    required this.categoria,
    required this.stories,
    required this.categoriaNome,
    required this.index,
  });

  final int? loadingIndex;
  final CategoriaMenu categoria;
  final List<StoryModel> stories;
  final String categoriaNome;
  final int index;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(left: 8),
      child: Column(
        children: [
          DoubleBorderIcon(
            loading: loadingIndex == index,
            isVisualized: stories.every((s) => s.visualizado),
            borderColor: ColorOutlet.paper,
            padding: 0,
            backgroundColor: getFilteredColor(categoria.cor),
            viewingBorder:
                stories.any((s) => !s.visualizado) && loadingIndex != index
                    ? ColorOutlet.contentPrimary
                    : ColorOutlet.systemBorderDisabled,
            child: ClipRRect(
              borderRadius: BorderRadius.circular(88),
              child: Container(
                alignment: Alignment.bottomRight,
                child: Image.asset(categoria.fotoPequena, height: 48, width: 48),
              ),
            ),
          ),
          const SizedBox(height: 8),
          TextPattern.customText(text: categoriaNome, fontSize: 12),
        ],
      ),
    );
  }
}
