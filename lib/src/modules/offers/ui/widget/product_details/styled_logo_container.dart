import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

import '../../../../../../theme/color_outlet.dart';

class StyledLogoContainer extends StatelessWidget {
  final String logo;
  final String plataformName;
  final double height;
  final double width;
  final double borderRadius;
  final double padding;
  final bool? needpadding;

  const StyledLogoContainer({
    super.key,
    required this.logo,
    required this.plataformName,
    this.height = 48,
    this.width = 48,
    this.borderRadius = 16,
    this.padding = 8,
    this.needpadding,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: height,
      width: height,
      padding: EdgeInsets.symmetric(horizontal: padding),
      decoration: BoxDecoration(
        color: ColorOutlet.paper,
        border: Border.all(
          color: ColorOutlet.systemBorderDisabled,
          width: 0.5,
        ),
        borderRadius: BorderRadius.circular(borderRadius),
      ),
      child: Padding(
        padding:
            needpadding == true
                ? const EdgeInsets.all(6.0)
                : EdgeInsets.zero,
        child:
            plataformName == 'Magazine Luiza'
                ? Image.asset(logo)
                : SvgPicture.asset(logo),
      ),
    );
  }
}
