import 'package:flutter/material.dart';
import 'package:promobell/src/modules/offers/ui/widget/product_details/blurred_unavailable_banner.dart';
import 'package:promobell/src/modules/offers/ui/widget/product_details/product_image_box.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../../../../../../theme/color_outlet.dart';
import '../../../../../components/text_pattern.dart';
import '../../../../../helpers/plataform_icons.dart';
import '../../../../../models/product.dart';
import '../../../controllers/offers_controller.dart';
import '../../../controllers/products_details_controller.dart';
import '../offers_page/like_button.dart';
import 'affiliate_link_button.dart';
import 'copy_coupon_button.dart';
import 'platform_coupon_row.dart';
import 'price_with_warning.dart';

class ProductContent extends StatefulWidget {
  final Product product;
  final OffersController controller;
  final bool isStory;
  final ProductDetailsController productDetailsController;

  const ProductContent({
    super.key,
    required this.product,
    required this.controller,
    required this.productDetailsController,
    this.isStory = false,
  });

  @override
  State<ProductContent> createState() => _ProductContentState();
}

class _ProductContentState extends State<ProductContent>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  bool messageWarning = true;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 200),
    );

    _initLikeState();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _initLikeState() async {
    final userEmail =
        Supabase.instance.client.auth.currentUser?.email ?? '';
    await widget.controller.initLikeState(
      widget.product.id,
      userEmail,
    );
  }

  void _handleLike() {
    final userEmail =
        Supabase.instance.client.auth.currentUser?.email ?? '';
    widget.controller.handleLike(widget.product.id, userEmail);
  }

  void toggleMessageWarning() {
    setState(() {
      messageWarning = !messageWarning;
    });
  }

  @override
  Widget build(BuildContext context) {
    const SizedBox sizedBox = SizedBox(height: 16);

    return AnimatedBuilder(
      animation: Listenable.merge([
        widget.controller,
        widget.productDetailsController,
      ]),
      builder: (context, _) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            PlatformRow(
              logo: PlatformIcons.fromName(widget.product.plataforma),
              criadoEm: widget.controller.getTimeToNow(
                widget.product.criadoEm,
              ),
              plataformName: widget.product.plataforma,
              idProduto: widget.product.id,
              isStory: widget.isStory,
            ),
            sizedBox,
            Expanded(
              child:
                  widget.product.invalidProduct
                      ? BlurredUnavailableBanner(
                        product: widget.product,
                      )
                      : ProductImageBox(
                        product: widget.product,
                        height: 216,
                      ),
            ),
            sizedBox,
            TextPattern.customText(
              text: widget.product.titulo,
              fontSize: 16,
              fontWeightOption: FontWeightOption.semiBold,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            sizedBox,
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Visibility(
                      visible: widget.product.precoAntigo != 0,
                      child: TextPattern.customText(
                        text: widget.controller.convertDoubleToString(
                          widget.product.precoAntigo,
                        ),
                        color: ColorOutlet.contentGhost,
                        fontSize: 14,
                        decoration: TextDecoration.lineThrough,
                        decorationColor: ColorOutlet.contentGhost,
                      ),
                    ),
                    Row(
                      children: [
                        TextPattern.customText(
                          text: widget.controller
                              .convertDoubleToString(
                                widget.product.precoAtual,
                              ),
                          color:
                              widget.product.invalidProduct
                                  ? ColorOutlet.contentGhost
                                  : ColorOutlet.contentPrimary,
                          fontSize: 24,
                          fontWeightOption: FontWeightOption.bold,
                        ),
                        PriceWithWarning(
                          productDetailsController:
                              widget.productDetailsController,
                          product: widget.product,
                        ),
                      ],
                    ),
                    sizedBox,
                  ],
                ),
                Visibility(
                  visible:
                      widget.product.cupom.trim().isNotEmpty &&
                      widget.product.cupom.toLowerCase().trim() !=
                          'sem cupom',
                  child: Column(
                    children: [
                      CopyCouponButton(
                        product: widget.product,
                        controller: widget.productDetailsController,
                      ),
                      sizedBox,
                    ],
                  ),
                ),
              ],
            ),
            Row(
              children: [
                Transform.translate(
                  offset: Offset(-4, 0),
                  child: LikeButton(
                    isLiked: widget.controller.isProductLiked(
                      widget.product.id,
                    ),
                    onTap: _handleLike,
                  ),
                ),
                Transform.translate(
                  offset: Offset(-4, 0),
                  child: SizedBox(
                    width: 40,
                    child: TextPattern.customText(
                      text:
                          widget.controller
                              .getLikesCount(widget.product.id)
                              .toString(),
                      color: ColorOutlet.contentSecondary,
                      fontSize: 14,
                      fontWeightOption: FontWeightOption.regular,
                    ),
                  ),
                ),
                SizedBox(width: 48),
                Expanded(
                  child: AffiliateLinkButton(
                    product: widget.product,
                    controller: widget.controller,
                    urlAfiliado: widget.product.urlAfiliado,
                  ),
                ),
              ],
            ),
          ],
        );
      },
    );
  }
}
