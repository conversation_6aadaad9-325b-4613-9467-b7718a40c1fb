import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';

import '../../../../../../theme/color_outlet.dart';
import '../../../../../../theme/line_height.dart';
import '../../../../../components/text_pattern.dart';
import '../../../../../core/base/controllers/base_controller/base_controller.dart';
import '../../../../../models/categorias_menu.dart';
import '../../../../../services/navigation/scroll_services.dart';
import '../../../../categories/categories_module.dart';
import '../../../../categories/ui/widgets/detail/category_name.dart';
import '../../../controllers/products_details_controller.dart';

class ProductCategoryLine extends StatelessWidget {
  final Color color;
  final CategoriaMenu categoria;
  final ProductDetailsController controller;

  const ProductCategoryLine({
    super.key,
    required this.controller,
    required this.color,
    required this.categoria,
  });

  @override
  Widget build(BuildContext context) {
    final BaseController controllerBase =
        Modular.get<BaseController>();
    final scrollService = Modular.get<ScrollService>();
    const double height = 64;

    return Stack(
      children: [
        Row(
          children: [
            Container(
              color:
                  controller.scrollProgress > 0.99
                      ? color
                      : Colors.transparent,
              height: height,
              width: controller.scrollProgress > 0.99 ? 17.3 : 16,
            ),
            Flexible(
              child: Container(
                color:
                    controller.scrollProgress > 0.035
                        ? ColorOutlet.paper
                        : Colors.transparent,
              ),
            ),
            Container(
              color:
                  controller.scrollProgress > 0.99
                      ? color
                      : Colors.transparent,
              height: height,
              width: controller.scrollProgress > 0.99 ? 17.3 : 16,
            ),
          ],
        ),
        Container(
          height: height,
          padding: EdgeInsets.only(left: 24),
          decoration: BoxDecoration(
            color: ColorOutlet.surface,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(24),
              topRight: Radius.circular(24),
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Flexible(
                child: CategoryName(
                  categoria: categoria,
                  spacing: true,
                ),
              ),
              TextButton(
                onPressed: () {
                  scrollService.saveScrollPosition('offersPage');
                  controllerBase.navPage(1);
                  Modular.to.pushNamed(
                    '/categories${CategoriesModule.detailsCategoryPage}',
                    arguments: categoria,
                  );
                },
                style: TextButton.styleFrom(
                  padding: EdgeInsets.symmetric(horizontal: 24),
                ),
                child: TextPattern.customText(
                  text: 'Mais ofertas',
                  fontSize: 14,
                  lineHeight: LineHeight.lh20f14,
                  fontWeightOption: FontWeightOption.regular,
                  color: ColorOutlet.contentPrimary,
                ),
              ),
            ],
          ),
        ),
        if (controller.scrollProgress > 0.99)
          Align(
            alignment: Alignment.bottomCenter,
            child: Container(
              height: 1,
              width: double.infinity,
              color: ColorOutlet.systemBorderDisabled,
            ),
          ),
      ],
    );
  }
}
