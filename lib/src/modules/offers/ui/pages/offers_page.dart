import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:promobell/src/modules/offers/ui/widget/offers_page/seach_state_ia.dart';

import '../../../../../theme/color_outlet.dart';
import '../../../../core/base/controllers/base_controller/base_controller.dart';
import '../../../../models/categorias_menu.dart';
import '../../../../models/product.dart';
import '../../../../services/navigation/scroll_services.dart';
import '../../../categories/ui/widgets/detail/sliver_app_bar_delegate.dart';
import '../../controllers/offers_controller.dart';
import '../../controllers/story/story_controller.dart';
import '../../offers_module.dart';
import '../widget/offers_page/animated_list_widget.dart';
import '../widget/offers_page/categoria_card_full_widget.dart';
import '../widget/offers_page/custom_campo_buscar.dart';
import '../widget/offers_page/filter_products_widget.dart';
import '../widget/offers_page/notification_bar.dart';
import '../widget/offers_page/product_card_widget.dart';
import '../widget/offers_page/search_header_delegate.dart';
import '../widget/shimmer/product_card_shimmer.dart';
import '../widget/storys/story_carousel.dart';
import 'empty_state_seach_widget.dart';
import 'offers_page_shimmer.dart';

class OffersPage extends StatefulWidget {
  const OffersPage({super.key});

  @override
  State<OffersPage> createState() => _OffersPageState();
}

class _OffersPageState extends State<OffersPage> {
  final controller = Modular.get<OffersController>();
  final storyController = Modular.get<StoryController>();
  final controllerBase = Modular.get<BaseController>();
  final scrollService = Modular.get<ScrollService>();

  ScrollController? _scrollController;
  bool _isFilterPinned = false;
  String? _lastFilter;
  // DateTime? _lastRefreshTime;

  @override
  void initState() {
    super.initState();
    _createScrollController();
  }

  @override
  void dispose() {
    _scrollController?.removeListener(_handleScroll);
    scrollService.unregisterScrollController('offersPage');
    _scrollController?.dispose();
    super.dispose();
  }

  void _createScrollController() {
    _scrollController?.removeListener(_handleScroll);
    _scrollController?.dispose();

    _scrollController = ScrollController(
      initialScrollOffset:
          scrollService.getScrollPosition('offersPage') ?? 0.0,
    );

    scrollService.registerScrollController(
      'offersPage',
      _scrollController!,
    );

    _scrollController!.addListener(_handleScroll);

    WidgetsBinding.instance.addPostFrameCallback((_) async {
      if (!mounted) return;

      if (controller.produtos.isEmpty) {
        await controller.refreshProdutos();
      }
    });
  }

  void _handleScroll() {
    if (!mounted || _scrollController == null) return;

    if (controller.searchQuery.isNotEmpty) return;

    scrollService.saveScrollPosition('offersPage');

    final maxScroll = _scrollController!.position.maxScrollExtent;
    final currentScroll = _scrollController!.position.pixels;

    if (maxScroll - currentScroll <= 300) {
      if (controller.isFiltroPersonalizadoAtivo) {
        controller.buscarProdutosComFiltrosPersonalizados(
          isCategoryFilters: false,
        );
      } else if (controller.searchQuery.isEmpty) {
        controller.fetchNextProdutos();
      }
    }
  }

  // Future<void> _onRefresh() async {
  //   final now = DateTime.now();

  //   if (_lastRefreshTime == null ||
  //       now.difference(_lastRefreshTime!).inMinutes >= 10) {
  //     _lastRefreshTime = now;
  //     storyController.refreshStory();
  //     await controller.refreshProdutos();
  //   } else {
  //     controller.setLoadingValue(true);
  //     await Future.delayed(const Duration(seconds: 1));
  //     controller.setLoadingValue(false);
  //   }
  // }

  @override
  Widget build(BuildContext context) {
    final topPadding = MediaQuery.of(context).padding.top;
    final topPaddingAndroid = MediaQuery.of(context).padding.top + 5;

    return Scaffold(
      body: AnimatedBuilder(
        animation: Listenable.merge([storyController, controller]),
        builder: (context, child) {
          if (controller.isLoadingInitial) {
            return OffersPageShimmer();
          }

          final items = controller.generateItens();
          final currentFilter = controller.selectedFilter;
          if (_lastFilter != currentFilter) {
            _lastFilter = currentFilter;
          }

          return SafeArea(
            top: false,
            bottom: false,
            child: CustomScrollView(
              physics: const BouncingScrollPhysics(),
              controller: _scrollController,
              slivers: [
                SliverPersistentHeader(
                  pinned: true,
                  delegate: SliverAppBarDelegate(
                    maxHeight:
                        Platform.isAndroid
                            ? topPaddingAndroid
                            : topPadding,
                    minHeight:
                        Platform.isAndroid
                            ? topPaddingAndroid
                            : topPadding,
                    child: Container(
                      color: ColorOutlet.surface,
                      height:
                          Platform.isAndroid
                              ? topPaddingAndroid
                              : topPadding,
                    ),
                  ),
                ),
                SliverToBoxAdapter(
                  child: Padding(
                    padding: const EdgeInsets.only(
                      bottom: 16,
                      left: 16,
                      right: 16,
                    ),
                    child: NotificationBar(
                      onTap: () {
                        if (!mounted) return;
                        if (_scrollController?.hasClients == true) {
                          scrollService.saveScrollPosition(
                            'offersPage',
                          );
                        }
                        Modular.to.pushNamed(
                          '/offers${OffersModule.notifications}',
                        );
                      },
                    ),
                  ),
                ),
                SliverPersistentHeader(
                  pinned: true,
                  delegate: SearchHeaderDelegate(
                    minHeigth: 48,
                    child: CampoBuscar(
                      isFixedFilter: _isFilterPinned,
                    ),
                  ),
                ),

                SliverVisibility(
                  visible: controller.searchQuery.isEmpty,
                  sliver: SliverToBoxAdapter(
                    child: Container(
                      color: ColorOutlet.surface,
                      child: const Padding(
                        padding: EdgeInsets.only(top: 16),
                        child: StoryCarousel(),
                      ),
                    ),
                  ),
                ),
                if (items.isNotEmpty &&
                    items.first is CategoriaMenu &&
                    controller.searchQuery.isEmpty &&
                    !controller.isFiltroPersonalizadoAtivo)
                  SliverToBoxAdapter(
                    child: CategoriaCardFullWidget(
                      categoria: items.first as CategoriaMenu,
                      isPrimary: true,
                    ),
                  ),

                SliverPersistentHeader(
                  pinned: true,
                  delegate: FilterPersistentHeaderDelegate(
                    onPinnedChange: (isPinned) {
                      if (mounted) {
                        setState(() {
                          _isFilterPinned = isPinned;
                        });
                      }
                    },
                  ),
                ),
                if ((controller.searchQuery.isNotEmpty ||
                        controller.isFiltroPersonalizadoAtivo) &&
                    items.isEmpty)
                  const SliverToBoxAdapter(
                    child: EmptyStateSeachWidget(),
                  )
                else if (controller.isSearching &&
                    controller.searchQuery.isNotEmpty)
                  const SliverToBoxAdapter(child: SeachStateIA())
                else
                  SliverList(
                    delegate: SliverChildBuilderDelegate(
                      (context, index) {
                        final bool isListaGeral =
                            controller.searchQuery.isEmpty &&
                            !controller.isFiltroPersonalizadoAtivo;

                        final item =
                            isListaGeral
                                ? (index + 1 < items.length
                                    ? items[index + 1]
                                    : null)
                                : items[index];

                        if (item == null) {
                          return const SizedBox.shrink();
                        }

                        if (controller.searchQuery.isNotEmpty) {
                          return _buildListItem(context, item);
                        }

                        return AnimatedListItem(
                          duration: const Duration(milliseconds: 0),
                          child: _buildListItem(context, item),
                        );
                      },
                      childCount:
                          controller.searchQuery.isEmpty &&
                                  !controller
                                      .isFiltroPersonalizadoAtivo
                              ? (items.length > 1
                                  ? items.length - 1
                                  : 0)
                              : items.length,
                    ),
                  ),

                const SliverToBoxAdapter(
                  child: SizedBox(height: 152),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildListItem(BuildContext context, dynamic item) {
    final isListaGeral =
        controller.searchQuery.isEmpty &&
        !controller.isFiltroPersonalizadoAtivo;

    if (item is Product) {
      return Padding(
        padding: const EdgeInsets.symmetric(
          horizontal: 16.0,
        ).copyWith(bottom: 16),
        child:
            controller.loadingFilter
                ? ProductCardShimmer()
                : ProductCardWidget(product: item),
      );
    }

    if (item is CategoriaMenu && isListaGeral) {
      return CategoriaCardFullWidget(categoria: item);
    }

    return const SizedBox.shrink();
  }
}

class FilterPersistentHeaderDelegate
    extends SliverPersistentHeaderDelegate {
  final ValueChanged<bool>? onPinnedChange;

  FilterPersistentHeaderDelegate({this.onPinnedChange});

  @override
  Widget build(
    BuildContext context,
    double shrinkOffset,
    bool overlapsContent,
  ) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      onPinnedChange?.call(overlapsContent);
    });

    return Container(
      color: ColorOutlet.surface,
      child: FilterProductsWidget(
        isPinned: overlapsContent,
        isCategoryFilters: true,
      ),
    );
  }

  @override
  double get maxExtent => 64;

  @override
  double get minExtent => 64;

  @override
  bool shouldRebuild(
    covariant FilterPersistentHeaderDelegate oldDelegate,
  ) {
    return false; // Evita reconstruções desnecessárias
  }
}
