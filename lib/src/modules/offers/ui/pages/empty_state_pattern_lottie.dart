import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';

import '../../../../components/text_pattern.dart';

class EmptyStatePatternLottie extends StatelessWidget {
  const EmptyStatePatternLottie({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Lottie.asset('assets/lottie/lottie-alerta.json', width: 120, height: 120, fit: BoxFit.cover),
        const SizedBox(height: 24),
        TextPattern.customText(text: 'Nenhum alerta por enquanto', fontSize: 16, fontWeightOption: FontWeightOption.semiBold),
        const SizedBox(height: 16),
        SizedBox(width: 300, child: TextPattern.customText(text: 'Quando rolar alguma oferta ou cupom, a gente vai te avisar por aqui.', fontSize: 14, textAlign: TextAlign.center)),
      ],
    );
  }
}
