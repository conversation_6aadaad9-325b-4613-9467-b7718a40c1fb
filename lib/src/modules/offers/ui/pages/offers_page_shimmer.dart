import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import '../../../../../theme/color_outlet.dart';
import '../../../../../theme/svg_icons.dart';
import '../../../../components/shimmer_widget.dart';
import '../../../../components/text_pattern.dart';
import '../widget/offers_page/notification_bar.dart';
import '../widget/shimmer/card_shimmer.dart';
import '../widget/shimmer/shimmer_buscar.dart';

class OffersPageShimmer extends StatelessWidget {
  const OffersPageShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SingleChildScrollView(
        child: SafeArea(
          child: Column(
            children: [
              Padding(
                padding: EdgeInsets.only(left: 16, right: 16),
                child: NotificationBar(onTap: () {}),
              ),
              ShimmerBuscar(),

              StoryCarouselShimmer(),
              const SizedBox(height: 24),
              Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16.0,
                  vertical: 8.0,
                ),
                child: Container(
                  height: 500,
                  width: double.infinity,
                  decoration: BoxDecoration(
                    color: ColorOutlet.systemBorderDisabled,
                    borderRadius: BorderRadius.circular(24),
                  ),
                  child: Column(
                    children: [
                      Padding(
                        padding: const EdgeInsets.all(24),
                        child: Row(
                          children: [
                            ShimmerWidget(height: 48, width: 48),
                            const SizedBox(width: 8),
                            ShimmerWidget(height: 16, width: 64),
                            Spacer(),
                            ShimmerWidget(height: 40, width: 74),
                          ],
                        ),
                      ),
                      const SizedBox(height: 200),
                      SizedBox(
                        height: 200,
                        child: ListView(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 24,
                          ),
                          shrinkWrap: true,
                          physics:
                              const NeverScrollableScrollPhysics(),
                          // list horizontal
                          scrollDirection: Axis.horizontal,
                          children: [
                            CardShimmer(),
                            const SizedBox(width: 16),
                            CardShimmer(),
                            const SizedBox(width: 16),
                            CardShimmer(),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: Row(
                  children: [
                    _buildFilterButton('Recentes'),
                    _buildFilterButton('Tem cupom'),
                    _buildFilterButton('Com frete grátis'),
                  ],
                ),
              ),
              Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16.0,
                  vertical: 8.0,
                ),
                child: Container(
                  height: 500,
                  width: double.infinity,
                  decoration: BoxDecoration(
                    color: ColorOutlet.systemBorderDisabled,
                    borderRadius: BorderRadius.circular(24),
                  ),
                  child: Column(
                    children: [
                      Padding(
                        padding: const EdgeInsets.all(24),
                        child: Row(
                          children: [
                            ShimmerWidget(height: 48, width: 48),
                            const SizedBox(width: 8),
                            ShimmerWidget(height: 16, width: 64),
                            Spacer(),
                            ShimmerWidget(height: 40, width: 74),
                          ],
                        ),
                      ),
                      const SizedBox(height: 200),
                      SizedBox(
                        height: 200,
                        child: ListView(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 24,
                          ),
                          shrinkWrap: true,
                          physics:
                              const NeverScrollableScrollPhysics(),
                          // list horizontal
                          scrollDirection: Axis.horizontal,
                          children: [
                            CardShimmer(),
                            const SizedBox(width: 16),
                            CardShimmer(),
                            const SizedBox(width: 16),
                            CardShimmer(),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class StoryCarouselShimmer extends StatelessWidget {
  const StoryCarouselShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        children: [
          SizedBox(width: 16),
          ...List.generate(
            8,
            (index) => Padding(
              padding: const EdgeInsets.only(right: 8),
              child: Column(
                children: [
                  Container(
                    height: 80,
                    width: 80,
                    decoration: BoxDecoration(
                      color: ColorOutlet.systemBorderDisabled,
                      borderRadius: BorderRadius.circular(96),
                    ),
                  ),
                  const SizedBox(height: 8),
                  Container(
                    height: 16,
                    width: 64,
                    decoration: BoxDecoration(
                      color: ColorOutlet.systemBorderDisabled,
                      borderRadius: BorderRadius.circular(96),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

Widget _buildFilterButton(String label) {
  return Container(
    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
    decoration: BoxDecoration(
      borderRadius: BorderRadius.circular(16),
    ),
    child: Row(
      children: [
        SvgPicture.asset(
          label == 'Recentes'
              ? SvgIcons.arrowRefresh
              : label == 'Melhor oferta'
              ? SvgIcons.financeDiscount
              : SvgIcons.markerShipping,
          colorFilter: ColorFilter.mode(
            label == 'Recentes'
                ? ColorOutlet.contentPrimary
                : ColorOutlet.contentSecondary,
            BlendMode.srcIn,
          ),
          width: 24,
          height: 24,
        ),
        const SizedBox(width: 4),
        TextPattern.customText(
          text: label,
          color:
              label == 'Recentes'
                  ? ColorOutlet.contentPrimary
                  : ColorOutlet.contentSecondary,
          fontSize: 13,
          fontWeightOption: FontWeightOption.semiBold,
        ),
      ],
    ),
  );
}
