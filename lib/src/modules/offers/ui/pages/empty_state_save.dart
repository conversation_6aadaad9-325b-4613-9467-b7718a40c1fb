import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';

import '../../../../components/text_pattern.dart';

class EmptyStateSave extends StatelessWidget {
  const EmptyStateSave({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Lottie.asset('assets/lottie/lottie-salvos.json', width: 120, height: 120, fit: BoxFit.cover),
        const SizedBox(height: 24),
        TextPattern.customText(text: 'Você ainda não salvou nada', fontSize: 16, fontWeightOption: FontWeightOption.semiBold),
        const SizedBox(height: 16),
        SizedBox(width: 300, child: TextPattern.customText(text: 'Curtiu um produto? Salva aqui a oferta pra encontrar e comprar com facilidade depois.', fontSize: 14, textAlign: TextAlign.center)),
      ],
    );
  }
}
