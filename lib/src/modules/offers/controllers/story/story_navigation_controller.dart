import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';

import '../../../../core/base/controllers/base_controller/base_controller.dart';
import '../../../../models/story_model.dart';
import 'story_animation_controller.dart';
import 'story_controller.dart';

class StoryNavigationController extends ChangeNotifier {
  // MARK: - Propriedades
  final StoryController storyController;
  final StoryAnimationController animationController;
  final BaseController controllerBase;
  late PageController categoryPageController;
  late PageController storyPageController;
  String _currentCategory = '';
  int _currentIndex = 0;
  List<String> _categories = [];
  bool _isTransitioning = false;

  // MARK: - Getters e Setters
  String get currentCategory => _currentCategory;
  int get currentIndex => _currentIndex;
  List<String> get categories => _categories;

  StoryModel? get currentStory {
    final stories = storyController.getStoriesByCategory(
      _currentCategory,
    );
    if (_currentIndex >= 0 && _currentIndex < stories.length) {
      return stories[_currentIndex];
    }
    return null;
  }

  int get safeCurrentIndex {
    final stories = storyController.getStoriesByCategory(
      _currentCategory,
    );
    return _currentIndex.clamp(
      0,
      stories.isEmpty ? 0 : stories.length - 1,
    );
  }

  bool get canNavigate {
    final stories = storyController.getStoriesByCategory(
      _currentCategory,
    );
    return stories.isNotEmpty && !_isTransitioning;
  }

  List<StoryModel> get currentStories {
    return storyController.getStoriesByCategory(_currentCategory);
  }

  set currentCategory(String value) {
    _currentCategory = value;
    notifyListeners();
  }

  StoryNavigationController({
    required this.storyController,
    required this.animationController,
    required this.controllerBase,
  });

  // MARK: - Métodos de navegação
  void initializeControllers({
    required String initialCategory,
    required List<String> orderedCategories,
    required int initialIndex,
  }) {
    _currentCategory = initialCategory;
    _currentIndex = initialIndex;
    _categories = orderedCategories;

    final initialCategoryIndex = _categories.indexOf(initialCategory);
    categoryPageController = PageController(
      initialPage: initialCategoryIndex,
    );
    storyPageController = PageController(initialPage: initialIndex);
  }

  void handleTapNavigation(bool isLeftSide) {
    if (animationController.isLongPressed || _isTransitioning) return;
    if (!storyPageController.hasClients) return;

    if (isLeftSide) {
      if (_currentIndex > 0) {
        storyPageController.previousPage(
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      } else {
        final currentCategoryIndex = _categories.indexOf(
          _currentCategory,
        );
        if (currentCategoryIndex > 0) {
          _isTransitioning = true;
          final previousCategory =
              _categories[currentCategoryIndex - 1];
          _currentCategory = previousCategory;
          _currentIndex = 0;
          if (storyPageController.hasClients) {
            storyPageController.jumpToPage(0);
          }
          _isTransitioning = false;
          notifyListeners();
          categoryPageController.previousPage(
            duration: const Duration(milliseconds: 200),
            curve: Curves.linear,
          );
        }
      }
    } else {
      nextStory(markAsViewed: true);
    }

    if (!animationController.isLongPressed) {
      animationController.resumeAnimation();
    }
  }

  void nextStory({bool markAsViewed = false}) async {
    if (animationController.isLongPressed || _isTransitioning) return;
    if (!storyPageController.hasClients) return;

    final stories = currentStories;
    if (_currentIndex < stories.length - 1) {
      if (markAsViewed) {
        final currentStory = stories[_currentIndex];
        storyController.markStoryAsViewed(currentStory.id);
      }
      storyPageController.nextPage(
        duration: const Duration(milliseconds: 200),
        curve: Curves.easeInOut,
      );
    } else {
      final currentCategoryIndex = _categories.indexOf(
        _currentCategory,
      );
      if (currentCategoryIndex < _categories.length - 1) {
        if (markAsViewed) {
          final currentStory = stories[_currentIndex];
          storyController.markStoryAsViewed(currentStory.id);
        }

        _isTransitioning = true;
        final nextCategory = _categories[currentCategoryIndex + 1];

        _currentCategory = nextCategory;
        _currentIndex = 0;
        _isTransitioning = false;
        notifyListeners();

        if (categoryPageController.hasClients) {
          categoryPageController.nextPage(
            duration: const Duration(milliseconds: 200),
            curve: Curves.linear,
          );
        }

        animationController.resetAnimation();
        animationController.startAnimation();
      } else {
        await _navigateBackToHome();
      }
    }
  }

  void handleCategoryChange(String newCategory) async {
    final stories = storyController.getStoriesByCategory(newCategory);

    _currentCategory = newCategory;
    _currentIndex = stories.isNotEmpty ? 0 : _currentIndex;

    animationController.resetAnimation();
    animationController.startAnimation();
    notifyListeners();
  }

  Future<void> _navigateBackToHome() async {
    _isTransitioning = true;
    storyController.savePosition(
      _currentCategory,
      _currentIndex,
      currentStories.length,
    );
    storyController.reorderCategories();
    animationController.setPageLoadState(false);
    storyController.getAllStories();
    Modular.to.navigate(
      '/home',
      arguments: {
        'maintainIndex': true,
        'tapPosition': storyController.tapPosition,
      },
    );
    controllerBase.navPage(0);
    _isTransitioning = false;
  }

  void handlePageChanged(int index) {
    _currentIndex = index;
    if (!_isTransitioning) {
      animationController.resetAnimation();
      animationController.startAnimation();
    }
    notifyListeners();
  }

  @override
  void dispose() {
    categoryPageController.dispose();
    storyPageController.dispose();
    super.dispose();
  }
}
