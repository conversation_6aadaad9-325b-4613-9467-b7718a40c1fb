import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../../../models/product.dart';
import '../../../../models/story_model.dart';
import '../../../../services/logs/app_logger.dart';
import '../../../../services/supabase/story/get/get_story.dart';
import '../../../../services/supabase/story/put/put_story.dart';

class StoryController extends ChangeNotifier {
  static final StoryController _instance =
      StoryController._internal();

  factory StoryController() {
    return _instance;
  }

  StoryController._internal();

  // MARK: - Propriedades
  final GetStory getStory = GetStory();
  final PutStory putStory = PutStory();

  // Mapas para armazenar stories por categoria e última posição visualizada
  final Map<String, List<StoryModel>> _storiesByCategory = {};
  final Map<String, int> _lastPositionByCategory = {};
  final bool _storyVisualizado = false;
  final List<String> _availableCategories = [];
  Offset _tapPosition = Offset.zero;
  bool _isInitialized = false;
  bool _loading = false;

  void lastTapPosition(Offset position) {
    _tapPosition = position;
    notifyListeners();
  }

  // MARK: - Getters
  Map<String, List<StoryModel>> get storiesByCategory =>
      _storiesByCategory;
  List<String> get availableCategories => _availableCategories;
  Offset get tapPosition => _tapPosition;
  bool get toryVisualizado => _storyVisualizado;
  bool get loading => _loading;

  /// Inicializa o controlador carregando todos os stories  @override
  Future<void> init() async {
    if (_isInitialized) return;

    await _loadSavedPositions();
    await getAllStories();

    _isInitialized = true;
  }

  /// Atualiza todos os stories, recarregando-os do servidor
  Future<void> refreshStory() async {
    try {
      _loading = true;
      notifyListeners();

      await getAllStories();
    } catch (e) {
      AppLogger.logError(
        'Erro ao atualizar stories',
        e,
        StackTrace.current,
      );
    } finally {
      _loading = false;
      notifyListeners();
    }
  }

  /// Busca todos os stories do servidor com tratamento de erro
  Future<void> getStories() async {
    try {
      await getAllStories();
    } catch (e) {
      AppLogger.logError(
        'Erro ao atualizar stories no refresh',
        e,
        StackTrace.current,
      );
    }
  }

  /// Marca um story específico como visualizado no servidor e atualiza localmente
  Future<void> markStoryAsViewed(int productId) async {
    try {
      await putStory.markStoryAsViewed(
        Supabase.instance.client.auth.currentUser?.email ?? '',
        productId,
      );

      _updateLocalStoryStatus(productId);

      // Não chama reorderCategories aqui, pois não queremos reordenar
      // durante a visualização
      notifyListeners();
    } catch (e) {
      AppLogger.logError(
        'Erro ao marcar a história como visualizada',
        e,
        StackTrace.current,
      );
    }
  }

  /// Verifica se uma categoria tem stories não visualizados
  bool hasUnviewedStories(String categoria) {
    if (!_storiesByCategory.containsKey(categoria)) {
      return false;
    }
    return _storiesByCategory[categoria]!.any(
      (story) => !story.visualizado,
    );
  }

  /// Converte um StoryModel para Product
  Product converterStoryParaPoduct(StoryModel story) {
    return Product(
      id: story.id,
      criadoEm: story.criadoEm,
      plataforma: story.plataforma,
      urlAfiliado: story.urlAfiliado,
      urlImagem: story.urlImagem,
      titulo: story.titulo,
      categoria: story.categoria,
      subcategoria: story.subcategoria,
      descricao: '',
      precoAtual: story.precoAtual,
      precoAntigo: story.precoAntigo,
      precoAlternativo: story.precoAlternativo,
      ativo: story.ativo,
      cupom: story.cupom,
      menorPreco: story.menorPreco,
      indicamos: story.indicamos,
      frete: story.frete,
      invalidProduct: story.invalidProduct,
      isStory: story.isStory,
    );
  }

  // MARK: - Métodos de Listagem

  List<StoryModel> getStoriesByCategory(String categoryName) {
    if (categoryName.isEmpty) return [];

    // Garante que sempre retornamos uma lista válida
    final stories = _storiesByCategory[categoryName] ?? [];

    // Ordena as stories se necessário
    stories.sort((a, b) => b.criadoEm.compareTo(a.criadoEm));

    return stories;
  }

  /// Busca todos os stories do servidor para o usuário atual
  Future<void> getAllStories() async {
    List<StoryModel> res = await getStory.fetchStories(
      Supabase.instance.client.auth.currentUser?.email ?? '',
    );
    addStories(res);
  }

  /// Adiciona uma lista de stories ao controlador, organizando-os por categoria
  void addStories(List<StoryModel> stories) {
    final Map<String, int> oldPositions = Map.from(
      _lastPositionByCategory,
    );

    _storiesByCategory.clear();
    _groupStoriesByCategory(stories);
    reorderCategories();

    _lastPositionByCategory.addAll(oldPositions);
    notifyListeners();
  }

  /// Agrupa os stories por categoria
  void _groupStoriesByCategory(List<StoryModel> stories) {
    for (var story in stories) {
      if (!_storiesByCategory.containsKey(story.categoria)) {
        _storiesByCategory[story.categoria] = [];
      }

      if (!_storiesByCategory[story.categoria]!.any(
        (s) => s.id == story.id,
      )) {
        _storiesByCategory[story.categoria]!.add(story);
      }
    }
  }

  // Reordena as categorias baseado em stories não visualizados e data
  void reorderCategories() {
    Set<String> categoriasCompletamenteVisualizadas = {};

    for (var entry in _storiesByCategory.entries) {
      bool todosVisualizados = entry.value.every(
        (story) => story.visualizado,
      );
      if (todosVisualizados) {
        categoriasCompletamenteVisualizadas.add(entry.key);
      }
    }

    if (categoriasCompletamenteVisualizadas.isEmpty) {
      return;
    }

    List<MapEntry<String, List<StoryModel>>> sortedEntries =
        _storiesByCategory.entries.toList()..sort((a, b) {
          bool todosVisualizadosA =
              categoriasCompletamenteVisualizadas.contains(a.key);
          bool todosVisualizadosB =
              categoriasCompletamenteVisualizadas.contains(b.key);

          if (todosVisualizadosA != todosVisualizadosB) {
            return todosVisualizadosA ? 1 : -1;
          }
          return b.value.first.criadoEm.compareTo(
            a.value.first.criadoEm,
          );
        });

    Map<String, List<StoryModel>> orderedCategories = {};

    for (var entry in sortedEntries.where(
      (e) => !categoriasCompletamenteVisualizadas.contains(e.key),
    )) {
      orderedCategories[entry.key] = entry.value;
    }
    for (var entry in sortedEntries.where(
      (e) => categoriasCompletamenteVisualizadas.contains(e.key),
    )) {
      orderedCategories[entry.key] = entry.value;
    }

    _storiesByCategory.clear();
    _storiesByCategory.addAll(orderedCategories);
    notifyListeners();
  }

  /// Atualiza o status de visualização de um story localmente
  void _updateLocalStoryStatus(int productId) {
    for (var stories in _storiesByCategory.values) {
      for (var story in stories) {
        if (story.id == productId && !story.visualizado) {
          story.visualizado = true;
          break;
        }
      }
    }
  }

  Future<void> _loadSavedPositions() async {
    final prefs = await SharedPreferences.getInstance();
    final String? savedPositions = prefs.getString('story_positions');

    if (savedPositions != null) {
      final Map<String, dynamic> positions = json.decode(
        savedPositions,
      );
      _lastPositionByCategory.clear();
      positions.forEach((key, value) {
        _lastPositionByCategory[key] = value as int;
      });
    }
  }

  // Modificar o savePosition para respeitar a mesma lógica
  Future<void> savePosition(
    String category,
    int index,
    int maxIndex,
  ) async {
    if (index < 0 || index >= maxIndex) {
      return;
    }

    final stories = _storiesByCategory[category] ?? [];

    // Verifica se há stories não visualizadas antes da posição atual
    int primeiroNaoVisualizado = stories.indexWhere(
      (story) => !story.visualizado,
    );

    if (primeiroNaoVisualizado != -1 &&
        primeiroNaoVisualizado < index) {
      index = primeiroNaoVisualizado;
    }

    _lastPositionByCategory[category] = index;
    await _savePositionsToPreferences();
    notifyListeners();
  }

  // Recupera a posição salva de uma categoria
  int getPosition(String category) {
    final stories = _storiesByCategory[category] ?? [];
    if (stories.isEmpty) {
      return 0;
    }
    int primeiroNaoVisualizado = stories.indexWhere(
      (story) => !story.visualizado,
    );
    if (primeiroNaoVisualizado != -1) {
      _lastPositionByCategory[category] = primeiroNaoVisualizado;
      _savePositionsToPreferences();

      return primeiroNaoVisualizado;
    }

    _lastPositionByCategory[category] = 0;
    _savePositionsToPreferences();

    return 0;
  }

  // Método para salvar as posições
  Future<void> _savePositionsToPreferences() async {
    final prefs = await SharedPreferences.getInstance();
    final String positions = json.encode(_lastPositionByCategory);
    await prefs.setString('story_positions', positions);
  }

  // Método para garantir que temos as stories carregadas
  Future<Map<String, List<StoryModel>>> getStoriesForCategory(
    String categoria,
  ) async {
    if (_storiesByCategory.isEmpty) {
      await getAllStories();
    }
    return _storiesByCategory;
  }

  // Método para obter stories de uma categoria específica
  List<StoryModel> getStoriesFromCategory(String categoria) {
    return _storiesByCategory[categoria] ?? [];
  }

  // Método para verificar se as stories estão carregadas
  bool isStoriesLoaded() {
    return _storiesByCategory.isNotEmpty;
  }
}
