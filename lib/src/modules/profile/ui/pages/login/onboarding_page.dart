import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:promobell/src/modules/profile/controllers/profile_controller.dart';
import 'package:promobell/src/modules/profile/ui/widgets/login/page_age.dart';
import 'package:promobell/src/modules/profile/ui/widgets/login/page_init.dart';
import 'package:promobell/src/modules/profile/ui/widgets/login/page_name.dart';
import 'package:promobell/src/modules/profile/ui/widgets/login/page_gender.dart';

class OnboardingPage extends StatefulWidget {
  const OnboardingPage({super.key});

  @override
  State<OnboardingPage> createState() => _OnboardingPageState();
}

class _OnboardingPageState extends State<OnboardingPage> {
  final controller = Modular.get<ProfileController>();
  final PageController _pageController = PageController();

  void _nextPage() {
    _pageController.nextPage(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  void _previousPage() {
    _pageController.previousPage(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: controller,
      builder: (context, _) {
        return GestureDetector(
          onTap: () => FocusScope.of(context).unfocus(),
          child: Scaffold(
            body: PageView(
              controller: _pageController,
              physics: const NeverScrollableScrollPhysics(),
              children: [
                PageInit(onNext: _nextPage, controller: controller),
                PageName(onNext: _nextPage, controller: controller),
                PageAge(
                  onBack: _previousPage,
                  onNext: _nextPage,
                  controller: controller,
                ),
                PageGender(
                  onBack: _previousPage,
                  controller: controller,
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
