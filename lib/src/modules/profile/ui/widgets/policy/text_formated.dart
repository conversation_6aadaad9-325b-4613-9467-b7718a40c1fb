import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../../../../theme/color_outlet.dart';
import '../../../../../components/text_pattern.dart';
import '../../pages/profile/about_the_promobell.dart';
import '../profile/whats_app_promo_card.dart';

class TextFormatter {
  static final emailRegex = RegExp(r'\b[\w\.-]+@[\w\.-]+\.\w{2,4}\b');
  static final List<String> boldWords = [
    "Promobell",
    "Amazon",
    "Mercado Livre",
    "Magazine Luiza",
    "Shopee",
    "WhatsApp",
    "Telegram",
  ];

  static final coloredWords = <String>[
    'Política de Privacidade',
    'Excluir sua conta',
    'canal oficial no WhatsApp',
    'instagram.com/promobelloficial',
  ];

  static final Map<String, GestureRecognizer> actionWords = {
    'instagram.com/promobelloficial':
        TapGestureRecognizer()
          ..onTap = () => openPromoBellInstagram(),
    'canal oficial no WhatsApp':
        TapGestureRecognizer()
          ..onTap = () => openPromoBellWhatsAppChannel(),
    'Política de Privacidade':
        TapGestureRecognizer()
          ..onTap =
              () => Modular.to.pushNamed('/profile/privacy_politics'),
    'Excluir sua conta':
        TapGestureRecognizer()
          ..onTap =
              () => Modular.to.pushNamed('/profile/delete_account'),
  };

  static List<TextSpan> buildTextSpans(
    String text, {
    bool highlightPrivacyPolicy = true,
  }) {
    List<TextSpan> spans = [];

    final List<String> effectiveColoredWords =
        highlightPrivacyPolicy
            ? coloredWords
            : coloredWords
                .where((word) => word != 'Política de Privacidade')
                .toList();

    final Map<String, GestureRecognizer> effectiveActionWords =
        Map.from(actionWords);
    if (!highlightPrivacyPolicy) {
      effectiveActionWords.remove('Política de Privacidade');
    }

    final combinedWords = [...boldWords, ...effectiveColoredWords];

    text.splitMapJoin(
      RegExp(
        "${emailRegex.pattern}|${combinedWords.map((word) {
          final escaped = RegExp.escape(word);
          return word.contains(' ') ? escaped : '\\b$escaped\\b';
        }).join('|')}",
      ),
      onMatch: (match) {
        final matchedText = match.group(0) ?? '';

        if (emailRegex.hasMatch(matchedText)) {
          spans.add(
            TextSpan(
              text: matchedText,
              style: TextStyle(
                color: ColorOutlet.contentPrimary,
                decorationColor: ColorOutlet.contentPrimary,
                decoration: TextDecoration.underline,
                fontWeight: FontWeight.w400,
                fontFamily: TextPattern().fontFamily,
              ),
              recognizer:
                  TapGestureRecognizer()
                    ..onTap =
                        () => launchUrl(
                          Uri.parse("mailto:$matchedText"),
                        ),
            ),
          );
        } else if (boldWords.contains(matchedText)) {
          spans.add(
            TextSpan(
              text: matchedText,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          );
        } else if (effectiveColoredWords.contains(matchedText)) {
          spans.add(
            TextSpan(
              text: matchedText,
              style: TextStyle(
                color: ColorOutlet.contentPrimary,
                decorationColor: ColorOutlet.contentPrimary,
                decoration: TextDecoration.underline,
                fontWeight: FontWeight.w400,
                fontFamily: TextPattern().fontFamily,
              ),
              recognizer: effectiveActionWords[matchedText],
            ),
          );
        }

        return '';
      },
      onNonMatch: (nonMatch) {
        spans.add(TextSpan(text: nonMatch));
        return '';
      },
    );

    return spans;
  }
}
