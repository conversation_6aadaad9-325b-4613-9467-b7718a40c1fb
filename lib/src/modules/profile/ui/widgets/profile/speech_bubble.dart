import 'package:flutter/material.dart';
import 'package:promobell/src/components/text_pattern.dart';

class SpeechBubble extends StatelessWidget {
  final String text;

  const SpeechBubble({super.key, required this.text});

  @override
  Widget build(BuildContext context) {
    return Stack(
      clipBehavior:
          Clip.none, // permite que o triângulo saia pra fora
      children: [
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: const Color(0xFFFFECC6),
            borderRadius: BorderRadius.circular(16),
          ),
          child: TextPattern.customText(text: text),
        ),

        // Triângulo no topo direito
        Positioned(
          top: -8,
          right: 24,
          child: CustomPaint(
            size: const Size(24, 12), // ← Aumentado aqui!
            painter: _TrianglePainter(const Color(0xFFFFECC6)),
          ),
        ),
      ],
    );
  }
}

class _TrianglePainter extends CustomPainter {
  final Color color;

  _TrianglePainter(this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..color = color;
    final path = Path();

    path.moveTo(0, size.height);
    path.lineTo(size.width / 2, 0);
    path.lineTo(size.width, size.height);
    path.close();

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
