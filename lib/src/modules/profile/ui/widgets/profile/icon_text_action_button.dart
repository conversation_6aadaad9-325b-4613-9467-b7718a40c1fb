import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import '../../../../../../theme/color_outlet.dart';
import '../../../../../../theme/line_height.dart';
import '../../../../../../theme/svg_icons.dart';

class IconTextActionButton extends StatelessWidget {
  final String icon;
  final String label;
  final VoidCallback onPressed;
  final bool isLoading;

  const IconTextActionButton({
    required this.onPressed,
    required this.icon,
    required this.label,
    this.isLoading = false,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 48,
      child: ElevatedButton(
        onPressed: onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: ColorOutlet.systemSurface,
          overlayColor: ColorOutlet.systemBorderDisabled.withValues(alpha: 0.3),
          padding: EdgeInsets.zero,
        ),
        child:
            isLoading
                ? Center(child: SizedBox(height: 24, width: 24, child: CircularProgressIndicator(strokeWidth: 1.5)))
                : Row(
                  children: [
                    SizedBox(width: 8),
                    SvgPicture.asset(icon),
                    SizedBox(width: 8),
                    Text(
                      label,
                      style: TextStyle(
                        fontSize: 13,
                        fontWeight: FontWeight.normal,
                        height: LineHeight.lh22f14,
                        color: ColorOutlet.contentSecondary,
                      ),
                    ),
                    const Spacer(),
                    SvgPicture.asset(SvgIcons.arrowRightTip, width: 24, height: 24),
                    SizedBox(width: 8),
                  ],
                ),
      ),
    );
  }
}
