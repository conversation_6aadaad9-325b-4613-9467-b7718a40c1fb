import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:promobell/src/modules/categories/ui/widgets/detail/custom_dialog.dart';
import 'package:promobell/src/modules/profile/controllers/profile_controller.dart';
import 'package:promobell/src/modules/profile/ui/widgets/close_button_onboarding.dart';
import 'package:promobell/src/modules/profile/ui/widgets/login/custom_text_input.dart';
import 'package:promobell/src/modules/profile/ui/widgets/login/navigation_buttons_row.dart';
import 'package:promobell/src/modules/profile/ui/widgets/login/onboarding_title_block.dart';
import 'package:promobell/src/modules/profile/ui/widgets/login/page_view_template.dart';
import 'package:promobell/src/modules/profile/ui/widgets/login/progress_badge.dart';

class PageName extends StatefulWidget {
  final VoidCallback onNext;
  final ProfileController controller;

  const PageName({
    required this.controller,
    required this.onNext,
    super.key,
  });

  @override
  State<PageName> createState() => _PageNameState();
}

class _PageNameState extends State<PageName> {
  late TextEditingController nameController;
  late TextEditingController surnameController;
  final nameFocusController = FocusNode();
  final surnameFocusController = FocusNode();
  final formKey = GlobalKey<FormState>();

  void _updateControllerValues() {
    widget.controller.setUserName(
      nameController.text,
      surnameController.text,
    );
  }

  @override
  void initState() {
    nameController = TextEditingController(
      text: widget.controller.userName,
    )..addListener(_updateControllerValues);
    surnameController = TextEditingController(
      text: widget.controller.userSurname,
    )..addListener(_updateControllerValues);
    super.initState();
  }

  @override
  void dispose() {
    nameController.dispose();
    surnameController.dispose();
    nameFocusController.dispose();
    surnameFocusController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final paddingBottom = MediaQuery.of(context).padding.bottom;
    return AnimatedBuilder(
      animation: Listenable.merge([
        nameController,
        surnameController,
        nameFocusController,
        surnameFocusController,
      ]),
      builder: (context, _) {
        return PageViewTemplate(
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  ProgressBadge(text: '1/3'),
                  CloseButtonOnboarding(
                    onPressed: () => _showWarningDialog(context),
                  ),
                ],
              ),
              OnboardingTitleBlock(
                title:
                    'Vamos lá!\nComo você gostaria que a gente te chamasse?',
                subtitle:
                    'Isso nos ajuda a deixar tudo mais personalizado pra você.',
              ),
              SizedBox(height: 32),
              Form(
                key: formKey,
                child: Column(
                  spacing: 24,
                  children: [
                    CustomTextInput(
                      controller: nameController,
                      focusNode: nameFocusController,
                      text: 'Nome',
                      validator: widget.controller.validarCampoNome,
                    ),
                    CustomTextInput(
                      focusNode: surnameFocusController,
                      controller: surnameController,
                      text: 'Sobrenome (opcional)',
                      validator:
                          widget.controller.validarCampoSobrenome,
                    ),
                  ],
                ),
              ),
              Spacer(),
              NavigationButtonsRow(
                onlyButton: true,
                onBack: () {},
                onNext: () {
                  if (formKey.currentState!.validate() &&
                      nameController.text.isNotEmpty) {
                    widget.controller.setUserName(
                      nameController.text,
                      surnameController.text,
                    );
                    widget.onNext();
                  }
                },
              ),
              SizedBox(height: paddingBottom),
            ],
          ),
        );
      },
    );
  }

  void _showWarningDialog(BuildContext context) {
    CustomDialog.show(
      context,
      title: 'Completar perfil depois',
      message:
          'Ao sair, as informações preenchidas serão descartadas. \n\nVocê poderá completá-las mais tarde nas configurações do perfil.',
      onConfirm: () {},
      onCancel: () {
        Modular.to.navigate('/home');
      },
      buttonOnly: false,
      textOnConfirm: 'Continuar editando',
      textOnCancel: 'Sair',
    );
  }
}
