import '../../../../../components/text_pattern.dart';
import '../../../../../models/categorias_menu.dart';
import '../../../../../../theme/color_outlet.dart';
import '../../../../../../theme/svg_icons.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

class CategoryName extends StatelessWidget {
  final CategoriaMenu categoria;
  final MainAxisAlignment mainAxisAlignment;
  final bool spacing;

  const CategoryName({
    super.key,
    this.mainAxisAlignment = MainAxisAlignment.center,
    required this.categoria,
    this.spacing = false,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: mainAxisAlignment,
      mainAxisSize: MainAxisSize.min,
      children: [
        if (spacing)
          Flexible(
            child: Padding(
              padding: const EdgeInsets.only(right: 5),
              child: TextPattern.customText(maxLines: 1, fontSize: 20, text: categoria.nome, overflow: TextOverflow.ellipsis, fontWeightOption: FontWeightOption.bold),
            ),
          ),
        if (!spacing) Padding(padding: const EdgeInsets.only(right: 5), child: TextPattern.customText(maxLines: 1, fontSize: 20, text: categoria.nome, overflow: TextOverflow.ellipsis, fontWeightOption: FontWeightOption.bold)),
        Padding(
          padding: const EdgeInsets.only(right: 8),
          child: SvgPicture.asset(SvgIcons.markerVerifiedFilled, height: 20, width: 20, colorFilter: ColorFilter.mode(ColorOutlet.contentPrimary, BlendMode.srcIn)),
        ),
      ],
    );
  }
}
