import '../../../../../models/categorias_menu.dart';
import '../../../../../../theme/color_outlet.dart';
import 'package:flutter/material.dart';

class CategoryImageContainer extends StatelessWidget {
  final Color color;
  final CategoriaMenu category;
  final double height;
  final double width;
  final double radius;
  final double widthImage;
  final double heightImage;
  final double radiusClippRect;
  final double widthLine;

  const CategoryImageContainer({
    super.key,
    required this.category,
    required this.color,
    this.height = 80,
    this.width = 80,
    this.radius = 24,
    this.radiusClippRect = 20,
    this.widthLine = 4,
    this.widthImage = 48,
    this.heightImage = 48,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: height,
      width: height,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.all(Radius.circular(radius)),
        border: Border.all(color: ColorOutlet.paper, width: widthLine),
      ),
      child: Container(
        alignment: Alignment.bottomRight,
        decoration: BoxDecoration(
          color: color,
          borderRadius: BorderRadius.all(Radius.circular(radiusClippRect)),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.only(bottomRight: Radius.circular(radiusClippRect)),
          child: Image.asset(
            category.fotoPequena,
            height: heightImage,
            width: widthImage,
            fit: BoxFit.fill,
          ),
        ),
      ),
    );
  }
}
