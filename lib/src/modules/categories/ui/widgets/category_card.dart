import '../../../../components/text_pattern.dart';
import '../../categories_module.dart';
import '../../../offers/controllers/offers_controller.dart';
import '../../../../../theme/color_outlet.dart';
import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import '../../../../components/vidio_widget_player.dart';
import '../../../../models/categorias_menu.dart';

class CategoryCard extends StatefulWidget {
  final CategoriaMenu categoria;
  final double? width;
  final double? height;
  final double maxWidth;
  final double maxHeight;
  final String image;
  final double imageHeight;
  final double imageWidth;
  final bool showVideo;
  final String? numberOfOffers;

  const CategoryCard({
    super.key,
    required this.categoria,
    this.width,
    this.height,
    this.maxWidth = double.maxFinite,
    this.maxHeight = double.maxFinite,
    required this.image,
    this.imageHeight = 88,
    this.imageWidth = 88,
    this.showVideo = false,
    this.numberOfOffers,
  });

  @override
  State<CategoryCard> createState() => _CategoryCardState();
}

class _CategoryCardState extends State<CategoryCard> {
  final OffersController controllerOffer = Modular.get<OffersController>();
  int _totalOfertas = 0;

  @override
  void initState() {
    super.initState();
    controllerOffer.filterProdutosPorCategoria(widget.categoria.nome);

    controllerOffer.getTotalPorCategoria(widget.categoria.nome).then((value) {
      if (mounted) {
        setState(() {
          _totalOfertas = value;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        try {
          Modular.to.pushNamed('/categories${CategoriesModule.detailsCategoryPage}', arguments: widget.categoria);
        } catch (e) {
          debugPrint('Erro na navegação: $e');
        }
      },
      child: Container(
        width: widget.width,
        height: widget.height,
        constraints: BoxConstraints(maxWidth: widget.maxWidth, maxHeight: widget.maxHeight),
        decoration: BoxDecoration(borderRadius: BorderRadius.circular(24)),
        clipBehavior: Clip.hardEdge,
        child: Stack(
          alignment: AlignmentDirectional.bottomEnd,
          children: [
            Stack(
              children: [
                Container(
                  width: widget.width,
                  height: widget.height,
                  constraints: BoxConstraints(maxWidth: widget.maxWidth, maxHeight: widget.maxHeight),
                  clipBehavior: Clip.hardEdge,
                  decoration: BoxDecoration(color: widget.categoria.cor, borderRadius: BorderRadius.circular(24)),
                ),
                Container(
                  clipBehavior: Clip.hardEdge,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(24),
                    color: const Color(0xFFFFFFFF).withValues(alpha: 0.56),
                  ),
                  width: widget.width,
                  height: widget.height,
                ),
              ],
            ),
            Stack(
              alignment: AlignmentDirectional.bottomEnd,
              children: [
                Container(
                  width: widget.imageWidth,
                  height: widget.imageHeight,
                  decoration: BoxDecoration(color: Colors.transparent),
                  clipBehavior: Clip.hardEdge,
                  child:
                      widget.showVideo
                          ? VideoWidget(videoPath: widget.categoria.video!)
                          : Image.asset(widget.image, fit: BoxFit.cover),
                ),
                Container(
                  clipBehavior: Clip.hardEdge,
                  alignment: Alignment.topLeft,
                  decoration: BoxDecoration(color: Colors.transparent),
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      TextPattern.customText(
                        text: widget.categoria.nome,
                        fontWeightOption: FontWeightOption.semiBold,
                        fontSize: 16,
                        textAlign: TextAlign.start,
                        color: ColorOutlet.contentSecondary,
                      ),
                      TextPattern.customText(
                        text: widget.numberOfOffers ?? '$_totalOfertas ${_totalOfertas == 1 ? 'oferta' : 'ofertas'}',
                        fontSize: 12,
                        textAlign: TextAlign.start,
                        color: ColorOutlet.contentGhost,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
