import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:promobell/src/components/text_pattern.dart';
import 'package:promobell/theme/color_outlet.dart';

import '../../../../core/base/controllers/base_controller/base_controller.dart';
import '../../../../models/categorias_menu.dart';
import '../../../../services/navigation/scroll_services.dart';
import '../widgets/category_card.dart';

class CategoriesPage extends StatefulWidget {
  const CategoriesPage({super.key});

  @override
  State<CategoriesPage> createState() => _CategoriesPageState();
}

class _CategoriesPageState extends State<CategoriesPage> {
  final BaseController controllerBase = Modular.get<BaseController>();
  final ScrollService _scrollService = Modular.get<ScrollService>();
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _scrollService.registerScrollController(
      'categoriesPage',
      _scrollController,
    );
  }

  @override
  void dispose() {
    _scrollService.unregisterScrollController('categoriesPage');
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        double widthCard = MediaQuery.of(context).size.width * 0.43;
        double topPadding = MediaQuery.of(context).padding.top;
        double paddingBottom = MediaQuery.of(context).padding.bottom;

        return Scaffold(
          body: Stack(
            children: [
              AnimationLimiter(
                child: ListView(
                  padding: EdgeInsets.only(
                    top: topPadding + 16,
                    bottom: 0,
                  ),
                  controller: _scrollController,
                  children: AnimationConfiguration.toStaggeredList(
                    duration: const Duration(milliseconds: 500),
                    childAnimationBuilder:
                        (widget) => SlideAnimation(
                          verticalOffset: 50.0,
                          child: FadeInAnimation(child: widget),
                        ),
                    children: [
                      CategoryGrid(
                        widthCard: widthCard,
                        constraints: constraints.maxWidth,
                        indices: [0, 1, 0, 2],
                        isPrimary: true,
                      ),
                      SizedBox(height: 16),
                      CategoryGrid(
                        widthCard: widthCard,
                        constraints: constraints.maxWidth,
                        indices: [3, 5, 4, 6],
                      ),
                      SizedBox(height: 16),
                      CategoryGrid(
                        widthCard: widthCard,
                        constraints: constraints.maxWidth,
                        indices: [7, 9, 8, 10],
                      ),
                      SizedBox(height: 16),
                      CategoryGrid(
                        padding: true,
                        widthCard: widthCard,
                        constraints: constraints.maxWidth,
                        indices: [11, 13, 12, 14],
                      ),
                      SizedBox(height: paddingBottom),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}

class CategoryGrid extends StatelessWidget {
  final double constraints;
  final double widthCard;
  final bool padding;
  final List<int> indices;
  final bool isPrimary;

  const CategoryGrid({
    super.key,
    required this.widthCard,
    required this.constraints,
    required this.indices,
    this.padding = false,
    this.isPrimary = false,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding:
          padding ? EdgeInsets.only(bottom: 32) : EdgeInsets.zero,
      child: Row(
        spacing: 16,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Column(
            spacing: 16,
            children: [
              if (isPrimary)
                SizedBox(
                  height: 144,
                  width: 171,
                  child: Column(
                    spacing: 8,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      TextPattern.customText(
                        text: 'Siga e economize',
                        fontSize: 24,
                        fontWeightOption: FontWeightOption.bold,
                      ),
                      Row(
                        children: [
                          Expanded(
                            child: TextPattern.customText(
                              text:
                                  'Encontre ofertas nas \ncategorias que têm tudo a ver com você.',
                              fontSize: 14,
                              maxLines: 3,
                              color: ColorOutlet.contentGhost,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              if (!isPrimary)
                CategoryCard(
                  categoria: CategoriaMenu.categorias[indices[0]],
                  width: widthCard,
                  height: 144,
                  maxWidth: constraints,
                  image:
                      CategoriaMenu
                          .categorias[indices[0]]
                          .fotoPequena,
                ),
              CategoryCard(
                categoria: CategoriaMenu.categorias[indices[1]],
                width: widthCard,
                height: 199,
                maxWidth: constraints,
                image:
                    CategoriaMenu.categorias[indices[1]].fotoPequena,
              ),
            ],
          ),
          Column(
            spacing: 16,
            children: [
              CategoryCard(
                categoria: CategoriaMenu.categorias[indices[2]],
                width: widthCard,
                height: 199,
                maxWidth: constraints,
                image:
                    CategoriaMenu.categorias[indices[2]].fotoPequena,
              ),
              CategoryCard(
                categoria: CategoriaMenu.categorias[indices[3]],
                width: widthCard,
                height: 144,
                maxWidth: constraints,
                image:
                    CategoriaMenu.categorias[indices[3]].fotoPequena,
              ),
            ],
          ),
        ],
      ),
    );
  }
}
