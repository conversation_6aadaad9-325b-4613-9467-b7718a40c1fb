import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:lottie/lottie.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:vibration/vibration.dart';

import '../../theme/color_outlet.dart';
import '../modules/offers/controllers/offers_controller.dart';
import '../modules/offers/controllers/story/story_controller.dart';
import 'base/controllers/base_controller/base_controller.dart';

class SplashPage extends StatefulWidget {
  const SplashPage({super.key});

  @override
  State<SplashPage> createState() => _SplashPageState();
}

class _SplashPageState extends State<SplashPage> {
  final controller = Modular.get<OffersController>();
  final baseController = Modular.get<BaseController>();
  final storyController = Modular.get<StoryController>();

  @override
  void initState() {
    super.initState();
    _startVibrationGradually();
    _checkAuth();
  }

  Future<void> _startVibrationGradually() async {
    final hasVibrator = await Vibration.hasVibrator();
    if (!(hasVibrator)) return;

    // Início: leve toque ao começar a splash
    await Future.delayed(const Duration(milliseconds: 500));
    Vibration.vibrate(duration: 100); // leve expectativa

    // 1º sino (entrada com tremor)
    await Future.delayed(const Duration(milliseconds: 400));
    Vibration.vibrate(duration: 300);
    await Future.delayed(const Duration(milliseconds: 100));
    Vibration.vibrate(duration: 200);

    // 2º sino (agitação aumentando)
    await Future.delayed(const Duration(milliseconds: 200));
    Vibration.vibrate(duration: 400);
    await Future.delayed(const Duration(milliseconds: 100));
    Vibration.vibrate(duration: 300);

    // 3º sino (pico da trepidação)
    await Future.delayed(const Duration(milliseconds: 150));
    Vibration.vibrate(duration: 500);
    await Future.delayed(const Duration(milliseconds: 80));
    Vibration.vibrate(duration: 250);

    // Toque final com impacto prolongado
    await Future.delayed(const Duration(milliseconds: 300));
    Vibration.vibrate(duration: 600); // vibração dramática
  }

  Future<void> _checkAuth() async {
    storyController.getAllStories();

    final prefs = await SharedPreferences.getInstance();
    final accessToken = prefs.getString('accessToken');

    await Future.delayed(const Duration(seconds: 3));
    if (mounted) {
      if (accessToken != null) {
        Modular.to.navigate('/home');
      } else {
        Modular.to.navigate('/profile/login');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ColorOutlet.contentPrimary,
      body: Center(
        child: Lottie.asset(
          'assets/lottie/lottie-logo.json',
          width: 120,
          height: 120,
          fit: BoxFit.cover,
          repeat: true,
          animate: true,
        ),
      ),
    );
  }
}
