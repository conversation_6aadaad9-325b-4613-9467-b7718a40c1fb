import 'package:flutter/material.dart';
import 'package:sqflite/sqflite.dart';

import '../../models/notification_storage_service.dart';

late Database database;

class InitSqflite extends ChangeNotifier {
  Future<void> init() async {
    database = await openDatabase(
      'promobell.db',
      version: 2,
      onCreate: (db, version) {
        db.execute('''
          CREATE TABLE storeNotifications(
            id INTEGER,
            title TEXT,
            body TEXT,
            date TEXT,
            isRead INTEGER,
            email TEXT,
            idProduto INTEGER
          )
        ''');
      },
      onUpgrade: (db, oldVersion, newVersion) async {
        if (oldVersion < 2) {
          await db.execute(
            "ALTER TABLE storeNotifications ADD COLUMN email TEXT;",
          );
          await db.execute(
            "ALTER TABLE storeNotifications ADD COLUMN idProduto INTEGER;",
          );
        }
      },
    );
  }

  Future<void> insertNotification(
    NotificationStorageService notification,
  ) async {
    await database.insert(
      'storeNotifications',
      notification.toMap(),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  Future<List<NotificationStorageService>> getNotifications() async {
    final List<Map<String, dynamic>> maps = await database.query(
      'storeNotifications',
    );
    return List.generate(maps.length, (i) {
      return NotificationStorageService(
        id: maps[i]['id'],
        title: maps[i]['title'],
        body: maps[i]['body'],
        date: maps[i]['date'],
        isRead: maps[i]['isRead'] == 1 ? 1 : 0,
        email: maps[i]['email'],
        idProduto: maps[i]['idProduto'],
      );
    });
  }

  Future<List<NotificationStorageService>> setReadNotification(
    NotificationStorageService notificationStorageService,
  ) async {
    await database.update(
      'storeNotifications',
      {'isRead': 1},
      where: 'title = ? AND body = ? AND date = ?',
      whereArgs: [
        notificationStorageService.title,
        notificationStorageService.body,
        notificationStorageService.date,
      ],
    );
    return getNotifications();
  }

  Future<void> setAllReadNotification() async {
    await database.update('storeNotifications', {'isRead': 1});
  }

  Future<void> deleteNotification() async {
    await database.delete(
      'storeNotifications',
      where: 'date < ?',
      whereArgs: [
        DateTime.now()
            .subtract(const Duration(days: 15))
            .toIso8601String(),
      ],
    );
  }

  Future<void> deleteAllNotification() async {
    await database.delete('storeNotifications');
  }
}
