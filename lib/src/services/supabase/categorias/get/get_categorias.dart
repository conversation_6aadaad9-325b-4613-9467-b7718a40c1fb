import 'package:promobell/src/models/categorias.dart';
import 'package:promobell/src/models/subcategoria.dart';

import '../../../logs/app_logger.dart';
import '../../db/db.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class GetCategorias {
  final SupabaseClient _supabase = Supabase.instance.client;
  DB db = DB();

  Future<int> getCategoryFollowersCount(int categoryId) async {
    final response = await _supabase.rpc(
      'count_followers',
      params: {'category_id_param': categoryId},
    );
    return response; // Retorna a contagem de seguidores
  }

  Future<bool> isFollowingCategory(
    int categoryId,
    String userEmail,
  ) async {
    try {
      final response = await _supabase.rpc(
        'is_following',
        params: {
          'category_id_param': categoryId,
          'user_email_param': userEmail,
        },
      );
      return response;
    } catch (e) {
      AppLogger.logError(
        'Erro ao verificar se o usuário está seguindo a categoria',
        e,
        StackTrace.current,
      );
      return false;
    }
  }

  Future<List<Categoria>> getCategoriasComSubcategorias() async {
    final categoriasResponse =
        await _supabase.from(db.tabelaDeCategorias).select();
    final subcategoriasResponse =
        await _supabase.from(db.tabelaDeSubcategorias).select();

    List<Subcategoria> subcategorias =
        (subcategoriasResponse as List)
            .map((json) => Subcategoria.fromJson(json))
            .toList();

    subcategorias.sort(
      (a, b) => a.idSubcategoria.compareTo(b.idSubcategoria),
    );

    List<Categoria> categorias =
        (categoriasResponse as List)
            .map((json) => Categoria.fromJson(json, subcategorias))
            .toList();

    return categorias;
  }
}
