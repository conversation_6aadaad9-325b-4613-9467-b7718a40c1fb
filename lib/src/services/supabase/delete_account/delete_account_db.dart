import 'package:supabase_flutter/supabase_flutter.dart';

import '../../../models/user_delete_profile.dart';
import '../../logs/app_logger.dart';
import '../db/db.dart';

class DeleteAccountDB {
  final SupabaseClient _supabase = Supabase.instance.client;
  DB db = DB();

  Future<void> saveDeletedUser({
    required UserDeleteProfile user,
  }) async {
    await _supabase.from(db.tabelaDeUsuariosDeletados).insert({
      'name': user.name,
      'email': user.email,
      'phone': user.phone,
      'image': user.image,
      'imageId': user.imageId,
      'accessToken': user.accessToken,
      'joinedDate': user.joinedDate?.toIso8601String(),
      'tokenFirebase': user.tokenFirebase,
      'razonDelete': user.razonDelete,
      'optionalReason': user.optionalReason,
    });
  }

  Future<bool> deleteUser({required String email}) async {
    try {
      // 1. Deletar da tabela user_profile
      await _supabase
          .from(DB().tabelaDeUsuarios)
          .delete()
          .eq('email', email);
      AppLogger.logInfo(
        'Usuário deletado da tabela user_profile: $email',
      );

      // 2. Nota: A exclusão do Authentication será feita pelo signOut
      // O Supabase não permite deletar usuários do Authentication via cliente
      // Por isso, vamos apenas fazer logout e limpar os dados locais
      AppLogger.logInfo(
        'Usuário removido da tabela. Authentication será limpo via signOut: $email',
      );

      return true;
    } catch (e) {
      AppLogger.logError(
        'Erro ao deletar usuário completamente',
        e,
        StackTrace.current,
      );
      return false;
    }
  }
}
