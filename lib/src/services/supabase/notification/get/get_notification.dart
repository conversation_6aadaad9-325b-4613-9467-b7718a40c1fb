import 'package:supabase_flutter/supabase_flutter.dart';

import '../../../../models/store_notifications_model.dart';
import '../../../logs/app_logger.dart';
import '../../db/db.dart';

class GetNotification {
  final DB db = DB();
  final SupabaseClient supabase = Supabase.instance.client;

  Future<List<StoreNotificationsModel>> getStoreNotifications15days() async {
    SupabaseClient supabase = Supabase.instance.client;
    try {
      List<StoreNotificationsModel> storeNotifications = [];
      final res = await supabase.from(db.tabelaDeNotifications).select().gte('date', DateTime.now().subtract(const Duration(days: 15)).toIso8601String());
      storeNotifications = res.map((e) => StoreNotificationsModel.fromMap(e)).toList();
      return storeNotifications;
    } on PostgrestException catch (state, e) {
      AppLogger.logError2('erro ao buscar getStoreNotifications15days', e);
      return [];
    }
  }
}
