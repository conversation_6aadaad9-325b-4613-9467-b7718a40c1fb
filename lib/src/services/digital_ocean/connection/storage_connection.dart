import 'dart:convert';
import 'dart:io';

import 'package:crypto/crypto.dart';
import 'package:dio/dio.dart';
import 'package:intl/intl.dart';

import '../../exceptions/app_exception.dart';
import '../../logs/app_logger.dart';
import '../config/digital_ocean_config.dart';
import 'i_storage_connection.dart';

class StorageConnection implements IStorageConnection {
  final DigitalOceanConfig config;
  final Dio _dio;

  StorageConnection({required this.config, Dio? dio}) : _dio = dio ?? Dio();
  @override
  Future<String> uploadFile(String path, List<int> bytes) async {
    try {
      final date = HttpDate.format(DateTime.now());
      final stringToSign = _generateStringToSign(path, date);
      final signature = _calculateSignature(stringToSign);

      final headers = {'Content-Type': 'image/png', 'Date': date, 'Authorization': 'AWS ${config.accessKey}:$signature', 'x-amz-acl': 'public-read'};

      AppLogger.logInfo('Iniciando upload para: ${config.spacesEndpoint}/$path');
      AppLogger.logInfo('Headers: $headers');

      final response = await _dio.put('${config.spacesEndpoint}/$path', data: bytes, options: Options(headers: headers, validateStatus: (status) => status! < 500));

      AppLogger.logInfo('Response status: ${response.statusCode}');
      AppLogger.logInfo('Response data: ${response.data}');

      if (response.statusCode == 200 || response.statusCode == 201) {
        return '${config.spacesEndpoint}/$path';
      }

      throw AppException('Upload falhou com status ${response.statusCode}');
    } catch (e, stack) {
      AppLogger.logError('Erro detalhado no upload', e, stack);
      rethrow;
    }
  }

  String _generateStringToSign(String path, String date) {
    return 'PUT\n\n'
        'image/png\n'
        '$date\n'
        'x-amz-acl:public-read\n'
        '/${config.bucketName}/$path';
  }

  String _calculateSignature(String stringToSign) {
    final hmac = Hmac(sha1, utf8.encode(config.secretKey));
    final signature = hmac.convert(utf8.encode(stringToSign));
    return base64.encode(signature.bytes);
  }

  // @override
  // Future<bool> deleteFile(String path) async {
  //   try {
  //     final response = await _dio.delete(
  //       path,
  //       options: Options(
  //         headers: {
  //           'Authorization': 'Bearer ${config.accessKey}',
  //         },
  //         validateStatus: (status) => status! < 500,
  //       ),
  //     );

  //     return response.statusCode == 204 || response.statusCode == 200;
  //   } catch (e, stack) {
  //     AppLogger.logError('Erro ao deletar arquivo', e, stack);
  //     return false;
  //   }
  // }

  @override
  Future<bool> deleteFile(String path) async {
    try {
      final region = config.region;
      final bucket = config.bucketName;
      final accessKey = config.accessKey;
      final secretKey = config.secretKey;
      final service = 's3';
      final host = '$bucket.$region.digitaloceanspaces.com';

      // Extrai e normaliza o objectPath
      final rawPath = Uri.parse(path).path.substring(1);
      final objectPath = Uri.encodeComponent(rawPath).replaceAll('%2F', '/');
      final endpoint = 'https://$host/$objectPath';

      final now = DateTime.now().toUtc();
      final date = DateFormat('yyyyMMdd').format(now);
      final iso8601 = DateFormat("yyyyMMdd'T'HHmmss'Z'").format(now);
      final credentialScope = '$date/$region/$service/aws4_request';

      final payloadHash = sha256.convert(utf8.encode('')).toString();

      final headers = {'host': host, 'x-amz-date': iso8601, 'x-amz-content-sha256': payloadHash};

      // Canonical headers e signedHeaders
      final sortedHeaderKeys = headers.keys.toList()..sort();

      final canonicalHeaders = sortedHeaderKeys.map((k) => '${k.toLowerCase()}:${headers[k]!.trim()}\n').join();

      final signedHeaders = sortedHeaderKeys.map((k) => k.toLowerCase()).join(';');

      // Canonical request
      final canonicalRequest = ['DELETE', '/$objectPath', '', canonicalHeaders, signedHeaders, payloadHash].join('\n');

      // String to sign
      final stringToSign = ['AWS4-HMAC-SHA256', iso8601, credentialScope, sha256.convert(utf8.encode(canonicalRequest)).toString()].join('\n');

      // Signature
      List<int> hmacSha256(List<int> key, String message) => Hmac(sha256, key).convert(utf8.encode(message)).bytes;

      final kDate = hmacSha256(utf8.encode('AWS4$secretKey'), date);
      final kRegion = hmacSha256(kDate, region);
      final kService = hmacSha256(kRegion, service);
      final kSigning = hmacSha256(kService, 'aws4_request');

      final signature = Hmac(sha256, kSigning).convert(utf8.encode(stringToSign)).toString();

      final authorization =
          'AWS4-HMAC-SHA256 '
          'Credential=$accessKey/$credentialScope, '
          'SignedHeaders=$signedHeaders, '
          'Signature=$signature';

      final signedHeadersMap = {...headers, 'Authorization': authorization};

      final response = await _dio.delete(endpoint, options: Options(headers: signedHeadersMap, validateStatus: (status) => status! < 500));

      return response.statusCode == 204 || response.statusCode == 200;
    } catch (e, stack) {
      AppLogger.logError('Erro ao deletar arquivo', e, stack);
      return false;
    }
  }

  Future<String?> getUserImageUrl(String userId) async {
    try {
      final response = await _dio.get('${config.spacesEndpoint}/profiles/$userId', options: Options(headers: _generateHeaders('profiles/$userId')));

      if (response.statusCode == 200) {
        final files = response.data['Contents'] as List;
        if (files.isNotEmpty) {
          // Pega o arquivo mais recente baseado no timestamp
          final latestFile = files.map((f) => f['Key'] as String).where((key) => key.endsWith('.png')).reduce((a, b) {
            final timeA = int.parse(a.split('/').last.split('.').first);
            final timeB = int.parse(b.split('/').last.split('.').first);
            return timeA > timeB ? a : b;
          });

          return '${config.spacesEndpoint}/$latestFile';
        }
      }
      return null;
    } catch (e, stack) {
      AppLogger.logError('Erro ao buscar imagem do usuário', e, stack);
      return null;
    }
  }

  Map<String, String> _generateHeaders(String path) {
    final date = HttpDate.format(DateTime.now());
    final stringToSign = _generateStringToSign(path, date);
    final signature = _calculateSignature(stringToSign);

    return {'Content-Type': 'image/png', 'Date': date, 'Authorization': 'AWS ${config.accessKey}:$signature', 'x-amz-acl': 'public-read'};
  }
}
