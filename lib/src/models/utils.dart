import 'dart:convert';

class Utils {
  final String version;
  final String criticalVersion;

  Utils({required this.version, required this.criticalVersion});

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'VERSION': version,
      'CRITICALVERSION': criticalVersion,
    };
  }

  factory Utils.fromMap(Map<String, dynamic> map) {
    return Utils(
      version: map['VERSION'] as String,
      criticalVersion: map['CRITICALVERSION'] as String,
    );
  }

  String toJson() => json.encode(toMap());

  factory Utils.fromJson(String source) =>
      Utils.fromMap(json.decode(source) as Map<String, dynamic>);

  factory Utils.empty() {
    return Utils(version: '', criticalVersion: '');
  }
}
