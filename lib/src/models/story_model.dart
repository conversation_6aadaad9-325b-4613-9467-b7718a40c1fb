import 'dart:convert';

class StoryModel {
  final int id;
  final DateTime criadoEm;
  final String plataforma;
  final String urlAfiliado;
  final String urlImagem;
  final String titulo;
  final String categoria;
  final String subcategoria;

  final double precoAtual;
  final double precoAntigo;
  final double precoAlternativo;
  final bool ativo;
  final String cupom;
  final bool menorPreco;
  final bool indicamos;
  final bool frete;
  final bool invalidProduct;
  final bool isStory;
  bool visualizado;
  final int likeCount;

  StoryModel({
    required this.id,
    required this.criadoEm,
    required this.plataforma,
    required this.urlAfiliado,
    required this.urlImagem,
    required this.titulo,
    required this.categoria,
    required this.subcategoria,

    required this.precoAtual,
    required this.precoAntigo,
    required this.precoAlternativo,
    required this.ativo,
    required this.cupom,
    required this.menorPreco,
    required this.indicamos,
    required this.frete,
    required this.invalidProduct,
    required this.isStory,
    required this.visualizado,
    required this.likeCount,
  });

  factory StoryModel.fromMap(Map<String, dynamic> map) {
    return StoryModel(
      id: map['id'] as int,
      criadoEm: DateTime.parse(map['criado_em']),
      plataforma: map['plataforma'] as String,
      urlAfiliado: map['url_afiliado'] as String,
      urlImagem: map['url_imagem'] as String,
      titulo: map['titulo'] as String,
      categoria: map['categoria'] as String,
      subcategoria: map['subcategoria'] as String,

      precoAtual:
          map['preco_atual'] != null
              ? (map['preco_atual'] is int
                  ? map['preco_atual'].toDouble()
                  : map['preco_atual'])
              : 0.0,
      precoAntigo:
          map['preco_antigo'] != null
              ? (map['preco_antigo'] is int
                  ? map['preco_antigo'].toDouble()
                  : map['preco_antigo'])
              : 0.0,
      precoAlternativo:
          map['preco_alternativo'] != null
              ? (map['preco_alternativo'] is int
                  ? map['preco_alternativo'].toDouble()
                  : map['preco_alternativo'])
              : 0.0,
      ativo: map['ativo'] as bool,
      cupom: map['cupom'] as String,
      menorPreco: map['menor_preco'] as bool,
      indicamos: map['indicamos'] as bool,
      frete: map['frete'] as bool,
      invalidProduct: map['invalid_product'] as bool,
      isStory: map['is_story'] as bool,
      visualizado: map['visualizado'] as bool,
      likeCount: map['like_count'] as int,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'criadoEm': criadoEm.toIso8601String(),
      'plataforma': plataforma,
      'urlAfiliado': urlAfiliado,
      'urlImagem': urlImagem,
      'titulo': titulo,
      'categoria': categoria,
      'subcategoria': subcategoria,
      'precoAtual': precoAtual,
      'precoAntigo': precoAntigo,
      'precoAlternativo': precoAlternativo,
      'ativo': ativo,
      'cupom': cupom,
      'menorPreco': menorPreco,
      'indicamos': indicamos,
      'frete': frete,
      'invalidProduct': invalidProduct,
      'isStory': isStory,
      'visualizado': visualizado,
      'likeCount': likeCount,
    };
  }

  String toJson() => json.encode(toMap());

  factory StoryModel.fromJson(String source) =>
      StoryModel.fromMap(json.decode(source));
}
