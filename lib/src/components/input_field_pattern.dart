import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:promobell/src/components/text_pattern.dart';
import '../../theme/color_outlet.dart';
import '../../theme/line_height.dart';

class InputFieldPattern extends StatefulWidget {
  final String? initialValue;
  final TextEditingController? controller;
  final String? hintText;
  final Color? textColor;
  final String? Function(String?)? validator;
  final Function()? onTapOutside;
  final bool? enabled;
  final Widget? suffixIcon;
  final Widget? suffixIconInput;
  final Function()? suffixIconTap;
  final List<TextInputFormatter>? inputFormatters;
  final Function()? onTap;
  final bool readOnly;

  const InputFieldPattern({
    super.key,
    this.initialValue,
    this.controller,
    this.hintText,
    this.textColor,
    this.validator,
    this.onTapOutside,
    this.enabled = true,
    this.suffixIcon,
    this.suffixIconInput,
    this.suffixIconTap,
    this.inputFormatters,
    this.onTap,
    this.readOnly = false,
  });

  @override
  State<InputFieldPattern> createState() => _InputFieldPatternState();
}

class _InputFieldPatternState extends State<InputFieldPattern> {
  @override
  Widget build(BuildContext context) {
    return FormField<String>(
      validator: widget.validator,
      builder: (field) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              decoration: BoxDecoration(
                color:
                    widget.enabled == false
                        ? ColorOutlet.surface
                        : Colors.transparent,
                borderRadius: BorderRadius.circular(16),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: TextFormField(
                      readOnly: widget.readOnly,
                      onTap: widget.onTap,
                      inputFormatters: widget.inputFormatters,
                      controller: widget.controller,
                      initialValue:
                          widget.controller == null
                              ? widget.initialValue
                              : null,
                      enabled: widget.enabled,
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w400,
                        height: LineHeight.lh20f14,
                        color:
                            widget.textColor ??
                            ColorOutlet.contentSecondary,
                        fontFamily: TextPattern().fontFamily,
                      ),
                      decoration: InputDecoration(
                        suffixIcon: widget.suffixIconInput,
                        hintText: widget.hintText,
                        hintStyle: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w400,
                          height: LineHeight.lh20f14,
                          fontFamily: TextPattern().fontFamily,
                          color: ColorOutlet.contentGhost,
                        ),
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 16,
                        ),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(16),
                          borderSide: const BorderSide(
                            color: ColorOutlet.contentGhost,
                            width: 1,
                          ),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(16),
                          borderSide: const BorderSide(
                            color: ColorOutlet.contentPrimary,
                            width: 1,
                          ),
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(16),
                          borderSide: const BorderSide(
                            color: ColorOutlet.contentGhost,
                            width: 1,
                          ),
                        ),
                        isDense: true,
                      ),
                      onChanged: (value) => field.didChange(value),
                      onTapOutside: (event) {
                        FocusScope.of(context).unfocus();
                        widget.onTapOutside?.call();
                      },
                    ),
                  ),
                  if (widget.suffixIcon != null)
                    Padding(
                      padding: const EdgeInsets.only(
                        left: 8,
                        right: 16,
                      ),
                      child: GestureDetector(
                        onTap: widget.suffixIconTap,
                        child: widget.suffixIcon,
                      ),
                    ),
                ],
              ),
            ),
            if (field.hasError)
              Padding(
                padding: const EdgeInsets.only(top: 4, left: 16),
                child: Text(
                  field.errorText ?? '',
                  style: const TextStyle(
                    color: Colors.red,
                    fontSize: 12,
                  ),
                ),
              ),
          ],
        );
      },
    );
  }
}
