import 'package:flutter/material.dart';

import '../../theme/color_outlet.dart';
import '../core/base/widgets/bottom_navigator_bar_widget.dart';
import '../core/base/widgets/no_connect.dart';

class ScaffoldTemplate extends StatefulWidget {
  final Widget body;
  final bool isPopNavigator;
  final Color? backgroundColor;
  final PreferredSizeWidget? appbar;
  final bool? isPopScope;
  final Widget? bottomNavigationBar;
  final bool isCategory;

  const ScaffoldTemplate({
    super.key,
    required this.body,
    required this.isPopNavigator,
    this.backgroundColor,
    this.appbar,
    this.isPopScope = false,
    this.bottomNavigationBar,
    this.isCategory = false,
  });

  @override
  State<ScaffoldTemplate> createState() => _ScaffoldTemplateState();
}

class _ScaffoldTemplateState extends State<ScaffoldTemplate> {
  @override
  Widget build(BuildContext context) {
    return NoConnect(
      child: PopScope(
        canPop: widget.isPopScope ?? false,
        child: Scaffold(
          resizeToAvoidBottomInset:
              false, // Evita que o teclado sobreponha o conteúdo
          extendBody:
              true, // Faz com que o body e o bottomNavigationBar sejam desenhados por trás
          extendBodyBehindAppBar: true, // Faz o mesmo para o appBar
          appBar: widget.appbar,
          backgroundColor:
              widget.backgroundColor ?? ColorOutlet.surface,
          body: Stack(
            children: [
              widget.body,
              Positioned(
                bottom: 0,
                left: 0,
                right: 0,
                child: Container(
                  height: 66,
                  decoration: BoxDecoration(
                    color: ColorOutlet.surface,
                    boxShadow: [
                      BoxShadow(
                        color: ColorOutlet.surface,
                        spreadRadius: 24,
                        blurRadius: 24,
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
          bottomNavigationBar: SafeArea(
            child: BottomNavigationBarWidget(
              isCategory: widget.isCategory,
            ),
          ),
        ),
      ),
    );
  }
}
