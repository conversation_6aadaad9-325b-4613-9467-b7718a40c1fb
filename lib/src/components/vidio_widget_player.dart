import 'dart:async';
import 'package:flutter/material.dart';
import 'package:video_player/video_player.dart';
import 'package:visibility_detector/visibility_detector.dart';

class VideoWidget extends StatefulWidget {
  final String videoPath;

  const VideoWidget({super.key, required this.videoPath});

  @override
  State<VideoWidget> createState() => VideoWidgetState();
}

class VideoWidgetState extends State<VideoWidget>
    with WidgetsBindingObserver {
  late VideoPlayerController _controller;
  Timer? _freezeChecker; // Timer para verificar travamentos
  Duration _lastPosition = Duration.zero;

  @override
  void initState() {
    super.initState();
    _controller =
        VideoPlayerController.asset(widget.videoPath)
          ..setLooping(true)
          ..initialize()
              .then((_) {
                if (mounted) setState(() {});
                _startFreezeChecker(); // Inicia o verificador de travamento
              })
              .catchError((error) {
                debugPrint('Erro ao carregar o vídeo: $error');
              });
  }

  void _startFreezeChecker() {
    _freezeChecker = Timer.periodic(Duration(seconds: 5), (timer) {
      if (_controller.value.isPlaying) {
        final currentPosition = _controller.value.position;
        if (currentPosition == _lastPosition) {
          // O vídeo está travado, reinicia
          debugPrint('Vídeo travado, reiniciando...');
          _controller.seekTo(Duration.zero);
          _controller.play();
        } else {
          _lastPosition = currentPosition;
        }
      }
    });
  }

  @override
  void dispose() {
    _freezeChecker
        ?.cancel(); // Cancela o verificador quando o widget é descartado
    if (_controller.value.isInitialized) {
      _controller.dispose();
    }
    super.dispose();
  }

  void _handleVisibilityChanged(VisibilityInfo info) {
    if (!mounted || !_controller.value.isInitialized) return;

    try {
      if (info.visibleFraction > 0.5 &&
          !_controller.value.isPlaying) {
        _controller.play();
      } else if (info.visibleFraction <= 0.5 &&
          _controller.value.isPlaying) {
        _controller.pause();
      }
    } catch (e) {
      debugPrint('Erro ao tentar reproduzir ou pausar o vídeo: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return VisibilityDetector(
      key: Key(widget.videoPath),
      onVisibilityChanged: _handleVisibilityChanged,
      child: Stack(
        children: [
          _controller.value.isInitialized
              ? SizedBox.expand(
                child: FittedBox(
                  fit: BoxFit.cover,
                  child: SizedBox(
                    width: _controller.value.size.width,
                    height: _controller.value.size.height,
                    child: VideoPlayer(_controller),
                  ),
                ),
              )
              : Container(color: Colors.black),
          // Gradiente na parte inferior
          Positioned.fill(
            child: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Colors.transparent,
                    Colors.black.withValues(alpha: 0.9),
                  ],
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
