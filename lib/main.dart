// import 'package:device_preview_plus/device_preview_plus.dart';
// import 'package:firebase_core/firebase_core.dart';
// import 'package:firebase_in_app_messaging/firebase_in_app_messaging.dart';
// import 'package:flutter/foundation.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter/services.dart';
// import 'package:flutter_modular/flutter_modular.dart';
// import 'package:flutter_native_splash/flutter_native_splash.dart';
// import 'package:supabase_flutter/supabase_flutter.dart';
// import 'package:timezone/data/latest.dart' as tz;

// import 'firebase_messaging_service.dart';
// import 'firebase_options.dart';
// import 'src/app/app_module.dart';
// import 'src/app/app_widget.dart';
// import 'src/services/sqflite.dart/init_sqflite.dart';
// import 'supa_base_config_keys.dart';

// void main() async {
//   WidgetsFlutterBinding.ensureInitialized();
//   try {
//     final supaBaseConfigs = SupaBaseConfigKeys(
//       apiUrl: const String.fromEnvironment('API_URL'),
//       apiAnonKey: const String.fromEnvironment('API_ANON_KEY'),
//       apiServiceRoles: const String.fromEnvironment('API_SERVICE_ROLES'),
//     );
//     tz.initializeTimeZones();
//     FlutterNativeSplash.remove();
//     SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);

//     await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);
//     final messagingService = FirebaseMessagingService();
//     await messagingService.initNotification();
//     await messagingService.getInstallationId();
//     final fiam = FirebaseInAppMessaging.instance;
//     await fiam.setMessagesSuppressed(false);

//     await Supabase.initialize(
//       url: supaBaseConfigs.apiUrl,
//       anonKey: supaBaseConfigs.apiAnonKey,
//       realtimeClientOptions: const RealtimeClientOptions(eventsPerSecond: 2),
//     );
//     await InitSqflite().init();
//   } catch (e) {
//     rethrow;
//   }

  // runApp(
  //   DevicePreview(
  //     enabled: !kReleaseMode, // Ativa apenas em modo debug
  //     builder: (context) => ModularApp(module: AppModule(), child: const AppWidget()),
  //   ),
  // );

//   runApp(ModularApp(module: AppModule(), child: const AppWidget()));
// }
